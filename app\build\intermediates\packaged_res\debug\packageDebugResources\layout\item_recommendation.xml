<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="horizontal"
    android:padding="8dp"
    android:background="@color/gaming_card_bg"
    android:layout_marginBottom="4dp">

    <!-- Priority Indicator -->
    <View
        android:id="@+id/priority_indicator"
        android:layout_width="4dp"
        android:layout_height="match_parent"
        android:background="@color/gaming_accent"
        android:layout_marginEnd="8dp" />

    <!-- Brawler Info -->
    <LinearLayout
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_weight="1"
        android:orientation="vertical">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:gravity="center_vertical">

            <TextView
                android:id="@+id/brawler_name"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="Brawler Name"
                android:textColor="@color/gaming_text_primary"
                android:textSize="14sp"
                android:textStyle="bold" />

            <TextView
                android:id="@+id/score_text"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="85%"
                android:textColor="@color/gaming_accent"
                android:textSize="12sp"
                android:textStyle="bold" />

        </LinearLayout>

        <TextView
            android:id="@+id/reason_text"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="Reason for recommendation"
            android:textColor="@color/gaming_text_secondary"
            android:textSize="11sp"
            android:layout_marginTop="2dp" />

    </LinearLayout>

</LinearLayout>
