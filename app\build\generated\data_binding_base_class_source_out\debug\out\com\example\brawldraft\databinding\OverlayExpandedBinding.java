// Generated by view binder compiler. Do not edit!
package com.example.brawldraft.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.Button;
import android.widget.ImageButton;
import android.widget.LinearLayout;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.example.brawldraft.R;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class OverlayExpandedBinding implements ViewBinding {
  @NonNull
  private final LinearLayout rootView;

  @NonNull
  public final ImageButton closeButton;

  @NonNull
  public final TextView draftStatus;

  @NonNull
  public final Button manualModeButton;

  @NonNull
  public final ImageButton minimizeButton;

  @NonNull
  public final TextView overlayTitle;

  @NonNull
  public final Button startCaptureButton;

  private OverlayExpandedBinding(@NonNull LinearLayout rootView, @NonNull ImageButton closeButton,
      @NonNull TextView draftStatus, @NonNull Button manualModeButton,
      @NonNull ImageButton minimizeButton, @NonNull TextView overlayTitle,
      @NonNull Button startCaptureButton) {
    this.rootView = rootView;
    this.closeButton = closeButton;
    this.draftStatus = draftStatus;
    this.manualModeButton = manualModeButton;
    this.minimizeButton = minimizeButton;
    this.overlayTitle = overlayTitle;
    this.startCaptureButton = startCaptureButton;
  }

  @Override
  @NonNull
  public LinearLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static OverlayExpandedBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static OverlayExpandedBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.overlay_expanded, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static OverlayExpandedBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.close_button;
      ImageButton closeButton = ViewBindings.findChildViewById(rootView, id);
      if (closeButton == null) {
        break missingId;
      }

      id = R.id.draft_status;
      TextView draftStatus = ViewBindings.findChildViewById(rootView, id);
      if (draftStatus == null) {
        break missingId;
      }

      id = R.id.manual_mode_button;
      Button manualModeButton = ViewBindings.findChildViewById(rootView, id);
      if (manualModeButton == null) {
        break missingId;
      }

      id = R.id.minimize_button;
      ImageButton minimizeButton = ViewBindings.findChildViewById(rootView, id);
      if (minimizeButton == null) {
        break missingId;
      }

      id = R.id.overlay_title;
      TextView overlayTitle = ViewBindings.findChildViewById(rootView, id);
      if (overlayTitle == null) {
        break missingId;
      }

      id = R.id.start_capture_button;
      Button startCaptureButton = ViewBindings.findChildViewById(rootView, id);
      if (startCaptureButton == null) {
        break missingId;
      }

      return new OverlayExpandedBinding((LinearLayout) rootView, closeButton, draftStatus,
          manualModeButton, minimizeButton, overlayTitle, startCaptureButton);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
