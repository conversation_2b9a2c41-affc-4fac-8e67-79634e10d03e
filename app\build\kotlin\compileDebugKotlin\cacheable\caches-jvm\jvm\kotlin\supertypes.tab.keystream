-com.example.brawldraft.RecommendationPriority-com.example.brawldraft.FloatingOverlayService#com.example.brawldraft.MainActivity6com.example.brawldraft.ScreenCapturePermissionActivity+com.example.brawldraft.ScreenCaptureService)com.example.brawldraft.data.BrawlerRarity'com.example.brawldraft.data.BrawlerType$com.example.brawldraft.data.TeamSide&com.example.brawldraft.data.DraftPhase&com.example.brawldraft.data.ActionType$com.example.brawldraft.data.GameMode9com.example.brawldraft.data.DraftStateUpdate.PhaseChanged8com.example.brawldraft.data.DraftStateUpdate.MapDetected:com.example.brawldraft.data.DraftStateUpdate.BrawlerBanned:com.example.brawldraft.data.DraftStateUpdate.BrawlerPicked7com.example.brawldraft.data.DraftStateUpdate.DraftReset=com.example.brawldraft.data.DraftStateUpdate.ManualCorrection/com.example.brawldraft.ui.RecommendationAdapterHcom.example.brawldraft.ui.RecommendationAdapter.RecommendationViewHolder<com.example.brawldraft.ui.RecommendationAdapter.DiffCallback'com.example.brawldraft.vision.GameState*com.example.brawldraft.vision.BrawlerState                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                           