package com.example.brawldraft

import android.app.Activity
import android.content.Intent
import android.os.Bundle
import android.util.Log

class ScreenCapturePermissionActivity : Activity() {
    
    companion object {
        private const val TAG = "ScreenCapturePermission"
        private const val REQUEST_SCREEN_CAPTURE = 1000
    }
    
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        
        val captureIntent = intent.getParcelableExtra<Intent>("capture_intent")
        if (captureIntent != null) {
            startActivityForResult(captureIntent, REQUEST_SCREEN_CAPTURE)
        } else {
            Log.e(TAG, "No capture intent provided")
            finish()
        }
    }
    
    override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
        super.onActivityResult(requestCode, resultCode, data)
        
        if (requestCode == REQUEST_SCREEN_CAPTURE) {
            // Send the result back to the overlay service via broadcast
            val intent = Intent("com.example.brawldraft.SCREEN_CAPTURE_RESULT").apply {
                putExtra("result_code", resultCode)
                putExtra("result_data", data)
            }
            sendBroadcast(intent)
            
            Log.d(TAG, "Screen capture permission result: $resultCode")
        }
        
        finish()
    }
}
