[{"key": "androidx/viewpager2/adapter/FragmentStateAdapter$1.class", "name": "androidx/viewpager2/adapter/FragmentStateAdapter$1.class", "size": 1862, "crc": 1365187382}, {"key": "androidx/viewpager2/adapter/FragmentStateAdapter$2.class", "name": "androidx/viewpager2/adapter/FragmentStateAdapter$2.class", "size": 1790, "crc": -845472744}, {"key": "androidx/viewpager2/adapter/FragmentStateAdapter$3.class", "name": "androidx/viewpager2/adapter/FragmentStateAdapter$3.class", "size": 808, "crc": -1052462133}, {"key": "androidx/viewpager2/adapter/FragmentStateAdapter$4.class", "name": "androidx/viewpager2/adapter/FragmentStateAdapter$4.class", "size": 1650, "crc": 1403980355}, {"key": "androidx/viewpager2/adapter/FragmentStateAdapter$DataSetChangeObserver.class", "name": "androidx/viewpager2/adapter/FragmentStateAdapter$DataSetChangeObserver.class", "size": 1655, "crc": 1360844231}, {"key": "androidx/viewpager2/adapter/FragmentStateAdapter$ExperimentalFragmentStateAdapterApi.class", "name": "androidx/viewpager2/adapter/FragmentStateAdapter$ExperimentalFragmentStateAdapterApi.class", "size": 596, "crc": 914912244}, {"key": "androidx/viewpager2/adapter/FragmentStateAdapter$FragmentEventDispatcher.class", "name": "androidx/viewpager2/adapter/FragmentStateAdapter$FragmentEventDispatcher.class", "size": 4635, "crc": 271306689}, {"key": "androidx/viewpager2/adapter/FragmentStateAdapter$FragmentMaxLifecycleEnforcer$1.class", "name": "androidx/viewpager2/adapter/FragmentStateAdapter$FragmentMaxLifecycleEnforcer$1.class", "size": 1306, "crc": -529811770}, {"key": "androidx/viewpager2/adapter/FragmentStateAdapter$FragmentMaxLifecycleEnforcer$2.class", "name": "androidx/viewpager2/adapter/FragmentStateAdapter$FragmentMaxLifecycleEnforcer$2.class", "size": 1248, "crc": -113646777}, {"key": "androidx/viewpager2/adapter/FragmentStateAdapter$FragmentMaxLifecycleEnforcer$3.class", "name": "androidx/viewpager2/adapter/FragmentStateAdapter$FragmentMaxLifecycleEnforcer$3.class", "size": 1464, "crc": -1296380939}, {"key": "androidx/viewpager2/adapter/FragmentStateAdapter$FragmentMaxLifecycleEnforcer.class", "name": "androidx/viewpager2/adapter/FragmentStateAdapter$FragmentMaxLifecycleEnforcer.class", "size": 6945, "crc": -857456871}, {"key": "androidx/viewpager2/adapter/FragmentStateAdapter$FragmentTransactionCallback$1.class", "name": "androidx/viewpager2/adapter/FragmentStateAdapter$FragmentTransactionCallback$1.class", "size": 834, "crc": -965919486}, {"key": "androidx/viewpager2/adapter/FragmentStateAdapter$FragmentTransactionCallback$OnPostEventListener.class", "name": "androidx/viewpager2/adapter/FragmentStateAdapter$FragmentTransactionCallback$OnPostEventListener.class", "size": 447, "crc": 1054539624}, {"key": "androidx/viewpager2/adapter/FragmentStateAdapter$FragmentTransactionCallback.class", "name": "androidx/viewpager2/adapter/FragmentStateAdapter$FragmentTransactionCallback.class", "size": 2280, "crc": 1411467455}, {"key": "androidx/viewpager2/adapter/FragmentStateAdapter.class", "name": "androidx/viewpager2/adapter/FragmentStateAdapter.class", "size": 17638, "crc": -1125972387}, {"key": "androidx/viewpager2/adapter/FragmentViewHolder.class", "name": "androidx/viewpager2/adapter/FragmentViewHolder.class", "size": 1573, "crc": -1054107435}, {"key": "androidx/viewpager2/adapter/StatefulAdapter.class", "name": "androidx/viewpager2/adapter/StatefulAdapter.class", "size": 365, "crc": 947800819}, {"key": "androidx/viewpager2/widget/AnimateLayoutChangeDetector$1.class", "name": "androidx/viewpager2/widget/AnimateLayoutChangeDetector$1.class", "size": 1027, "crc": 1237610935}, {"key": "androidx/viewpager2/widget/AnimateLayoutChangeDetector.class", "name": "androidx/viewpager2/widget/AnimateLayoutChangeDetector.class", "size": 3654, "crc": 240950864}, {"key": "androidx/viewpager2/widget/CompositeOnPageChangeCallback.class", "name": "androidx/viewpager2/widget/CompositeOnPageChangeCallback.class", "size": 2704, "crc": -1051717547}, {"key": "androidx/viewpager2/widget/CompositePageTransformer.class", "name": "androidx/viewpager2/widget/CompositePageTransformer.class", "size": 1629, "crc": 1507765286}, {"key": "androidx/viewpager2/widget/FakeDrag.class", "name": "androidx/viewpager2/widget/FakeDrag.class", "size": 3733, "crc": 1216112809}, {"key": "androidx/viewpager2/widget/MarginPageTransformer.class", "name": "androidx/viewpager2/widget/MarginPageTransformer.class", "size": 1957, "crc": 1958636187}, {"key": "androidx/viewpager2/widget/PageTransformerAdapter.class", "name": "androidx/viewpager2/widget/PageTransformerAdapter.class", "size": 2625, "crc": 1152927248}, {"key": "androidx/viewpager2/widget/ScrollEventAdapter$ScrollEventValues.class", "name": "androidx/viewpager2/widget/ScrollEventAdapter$ScrollEventValues.class", "size": 710, "crc": 905177897}, {"key": "androidx/viewpager2/widget/ScrollEventAdapter.class", "name": "androidx/viewpager2/widget/ScrollEventAdapter.class", "size": 8965, "crc": -1839784949}, {"key": "androidx/viewpager2/widget/ViewPager2$1.class", "name": "androidx/viewpager2/widget/ViewPager2$1.class", "size": 969, "crc": -1672653519}, {"key": "androidx/viewpager2/widget/ViewPager2$2.class", "name": "androidx/viewpager2/widget/ViewPager2$2.class", "size": 1281, "crc": -676254957}, {"key": "androidx/viewpager2/widget/ViewPager2$3.class", "name": "androidx/viewpager2/widget/ViewPager2$3.class", "size": 1089, "crc": -**********}, {"key": "androidx/viewpager2/widget/ViewPager2$4.class", "name": "androidx/viewpager2/widget/ViewPager2$4.class", "size": 1767, "crc": -**********}, {"key": "androidx/viewpager2/widget/ViewPager2$AccessibilityProvider.class", "name": "androidx/viewpager2/widget/ViewPager2$AccessibilityProvider.class", "size": 4395, "crc": -**********}, {"key": "androidx/viewpager2/widget/ViewPager2$BasicAccessibilityProvider.class", "name": "androidx/viewpager2/widget/ViewPager2$BasicAccessibilityProvider.class", "size": 2418, "crc": -**********}, {"key": "androidx/viewpager2/widget/ViewPager2$DataSetChangeObserver.class", "name": "androidx/viewpager2/widget/ViewPager2$DataSetChangeObserver.class", "size": 1579, "crc": **********}, {"key": "androidx/viewpager2/widget/ViewPager2$LinearLayoutManagerImpl.class", "name": "androidx/viewpager2/widget/ViewPager2$LinearLayoutManagerImpl.class", "size": 3723, "crc": -287022879}, {"key": "androidx/viewpager2/widget/ViewPager2$OffscreenPageLimit.class", "name": "androidx/viewpager2/widget/ViewPager2$OffscreenPageLimit.class", "size": 724, "crc": -**********}, {"key": "androidx/viewpager2/widget/ViewPager2$OnPageChangeCallback.class", "name": "androidx/viewpager2/widget/ViewPager2$OnPageChangeCallback.class", "size": 896, "crc": 196182523}, {"key": "androidx/viewpager2/widget/ViewPager2$Orientation.class", "name": "androidx/viewpager2/widget/ViewPager2$Orientation.class", "size": 652, "crc": 365806602}, {"key": "androidx/viewpager2/widget/ViewPager2$PageAwareAccessibilityProvider$1.class", "name": "androidx/viewpager2/widget/ViewPager2$PageAwareAccessibilityProvider$1.class", "size": 1568, "crc": -**********}, {"key": "androidx/viewpager2/widget/ViewPager2$PageAwareAccessibilityProvider$2.class", "name": "androidx/viewpager2/widget/ViewPager2$PageAwareAccessibilityProvider$2.class", "size": 1568, "crc": -493037467}, {"key": "androidx/viewpager2/widget/ViewPager2$PageAwareAccessibilityProvider$3.class", "name": "androidx/viewpager2/widget/ViewPager2$PageAwareAccessibilityProvider$3.class", "size": 1209, "crc": **********}, {"key": "androidx/viewpager2/widget/ViewPager2$PageAwareAccessibilityProvider.class", "name": "androidx/viewpager2/widget/ViewPager2$PageAwareAccessibilityProvider.class", "size": 9396, "crc": -134857645}, {"key": "androidx/viewpager2/widget/ViewPager2$PageTransformer.class", "name": "androidx/viewpager2/widget/ViewPager2$PageTransformer.class", "size": 372, "crc": -4539954}, {"key": "androidx/viewpager2/widget/ViewPager2$PagerSnapHelperImpl.class", "name": "androidx/viewpager2/widget/ViewPager2$PagerSnapHelperImpl.class", "size": 1156, "crc": **********}, {"key": "androidx/viewpager2/widget/ViewPager2$RecyclerViewImpl.class", "name": "androidx/viewpager2/widget/ViewPager2$RecyclerViewImpl.class", "size": 2254, "crc": -**********}, {"key": "androidx/viewpager2/widget/ViewPager2$SavedState$1.class", "name": "androidx/viewpager2/widget/ViewPager2$SavedState$1.class", "size": 1908, "crc": 977435821}, {"key": "androidx/viewpager2/widget/ViewPager2$SavedState.class", "name": "androidx/viewpager2/widget/ViewPager2$SavedState.class", "size": 2107, "crc": -650223238}, {"key": "androidx/viewpager2/widget/ViewPager2$ScrollState.class", "name": "androidx/viewpager2/widget/ViewPager2$ScrollState.class", "size": 652, "crc": -1460312045}, {"key": "androidx/viewpager2/widget/ViewPager2$SmoothScrollToPosition.class", "name": "androidx/viewpager2/widget/ViewPager2$SmoothScrollToPosition.class", "size": 879, "crc": 155300724}, {"key": "androidx/viewpager2/widget/ViewPager2.class", "name": "androidx/viewpager2/widget/ViewPager2.class", "size": 22122, "crc": -1241767196}, {"key": "androidx/viewpager2/widget/WindowInsetsApplier.class", "name": "androidx/viewpager2/widget/WindowInsetsApplier.class", "size": 2757, "crc": -559211281}, {"key": "META-INF/androidx.viewpager2_viewpager2.version", "name": "META-INF/androidx.viewpager2_viewpager2.version", "size": 13, "crc": 851388311}, {"key": "META-INF/viewpager2_release.kotlin_module", "name": "META-INF/viewpager2_release.kotlin_module", "size": 24, "crc": 1613429616}]