package com.example.brawldraft.assets

import android.content.Context
import android.graphics.Bitmap
import android.graphics.BitmapFactory
import android.util.Log
import android.util.LruCache
import com.example.brawldraft.data.Brawler
import com.example.brawldraft.data.BrawlerDatabase
import com.example.brawldraft.data.GameMap
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import java.io.IOException

class AssetManager(private val context: Context) {
    
    companion object {
        private const val TAG = "AssetManager"
        private const val BRAWLER_ICONS_PATH = "brawler_icons"
        private const val MAP_ICONS_PATH = "map_icons"
        private const val TEMPLATE_ICONS_PATH = "template_icons"
        private const val CACHE_SIZE = 50 // Number of bitmaps to cache
    }
    
    // LRU cache for loaded bitmaps
    private val bitmapCache = LruCache<String, Bitmap>(CACHE_SIZE)
    
    // Asset availability tracking
    private val availableAssets = mutableSetOf<String>()
    private var assetsScanned = false
    
    suspend fun initialize() = withContext(Dispatchers.IO) {
        try {
            Log.d(TAG, "Initializing asset manager...")
            scanAvailableAssets()
            Log.d(TAG, "Asset manager initialized. Found ${availableAssets.size} assets.")
        } catch (e: Exception) {
            Log.e(TAG, "Failed to initialize asset manager", e)
        }
    }
    
    private fun scanAvailableAssets() {
        try {
            // Scan brawler icons
            scanAssetsInPath(BRAWLER_ICONS_PATH)
            
            // Scan map icons
            scanAssetsInPath(MAP_ICONS_PATH)
            
            // Scan template icons
            scanAssetsInPath(TEMPLATE_ICONS_PATH)
            
            assetsScanned = true
        } catch (e: Exception) {
            Log.e(TAG, "Error scanning assets", e)
        }
    }
    
    private fun scanAssetsInPath(path: String) {
        try {
            val assetList = context.assets.list(path)
            if (assetList != null) {
                for (asset in assetList) {
                    val fullPath = "$path/$asset"
                    availableAssets.add(fullPath)
                    Log.d(TAG, "Found asset: $fullPath")
                }
            }
        } catch (e: IOException) {
            Log.w(TAG, "Path not found: $path")
        }
    }
    
    suspend fun getBrawlerIcon(brawler: Brawler): Bitmap? = withContext(Dispatchers.IO) {
        val cacheKey = "brawler_${brawler.id}"
        
        // Check cache first
        bitmapCache.get(cacheKey)?.let { return@withContext it }
        
        // Try to load from assets
        val assetPath = "$BRAWLER_ICONS_PATH/${brawler.id}.png"
        val bitmap = loadBitmapFromAssets(assetPath)
        
        if (bitmap != null) {
            bitmapCache.put(cacheKey, bitmap)
            return@withContext bitmap
        }
        
        // Fallback: generate placeholder
        val placeholder = generateBrawlerPlaceholder(brawler)
        bitmapCache.put(cacheKey, placeholder)
        placeholder
    }
    
    suspend fun getMapIcon(map: GameMap): Bitmap? = withContext(Dispatchers.IO) {
        val cacheKey = "map_${map.id}"
        
        // Check cache first
        bitmapCache.get(cacheKey)?.let { return@withContext it }
        
        // Try to load from assets
        val assetPath = "$MAP_ICONS_PATH/${map.id}.png"
        val bitmap = loadBitmapFromAssets(assetPath)
        
        if (bitmap != null) {
            bitmapCache.put(cacheKey, bitmap)
            return@withContext bitmap
        }
        
        // Fallback: generate placeholder
        val placeholder = generateMapPlaceholder(map)
        bitmapCache.put(cacheKey, placeholder)
        placeholder
    }
    
    suspend fun getTemplateIcon(brawler: Brawler): Bitmap? = withContext(Dispatchers.IO) {
        val cacheKey = "template_${brawler.id}"
        
        // Check cache first
        bitmapCache.get(cacheKey)?.let { return@withContext it }
        
        // Try to load from assets
        val assetPath = "$TEMPLATE_ICONS_PATH/${brawler.id}.png"
        val bitmap = loadBitmapFromAssets(assetPath)
        
        if (bitmap != null) {
            bitmapCache.put(cacheKey, bitmap)
            return@withContext bitmap
        }
        
        // Fallback: try brawler icon
        return@withContext getBrawlerIcon(brawler)
    }
    
    private fun loadBitmapFromAssets(assetPath: String): Bitmap? {
        return try {
            if (!assetsScanned || availableAssets.contains(assetPath)) {
                val inputStream = context.assets.open(assetPath)
                val bitmap = BitmapFactory.decodeStream(inputStream)
                inputStream.close()
                bitmap
            } else {
                null
            }
        } catch (e: IOException) {
            Log.d(TAG, "Asset not found: $assetPath")
            null
        } catch (e: Exception) {
            Log.e(TAG, "Error loading asset: $assetPath", e)
            null
        }
    }
    
    private fun generateBrawlerPlaceholder(brawler: Brawler): Bitmap {
        // Create a simple colored placeholder based on brawler rarity
        val size = 64
        val bitmap = Bitmap.createBitmap(size, size, Bitmap.Config.ARGB_8888)
        
        // Fill with color based on rarity
        val color = when (brawler.rarity) {
            com.example.brawldraft.data.BrawlerRarity.COMMON -> 0xFF808080.toInt()
            com.example.brawldraft.data.BrawlerRarity.RARE -> 0xFF00FF00.toInt()
            com.example.brawldraft.data.BrawlerRarity.SUPER_RARE -> 0xFF0080FF.toInt()
            com.example.brawldraft.data.BrawlerRarity.EPIC -> 0xFF8000FF.toInt()
            com.example.brawldraft.data.BrawlerRarity.MYTHIC -> 0xFFFF0080.toInt()
            com.example.brawldraft.data.BrawlerRarity.LEGENDARY -> 0xFFFFD700.toInt()
            com.example.brawldraft.data.BrawlerRarity.CHROMATIC -> 0xFFFF8000.toInt()
        }
        
        bitmap.eraseColor(color)
        return bitmap
    }
    
    private fun generateMapPlaceholder(map: GameMap): Bitmap {
        // Create a simple placeholder for maps
        val size = 64
        val bitmap = Bitmap.createBitmap(size, size, Bitmap.Config.ARGB_8888)
        
        // Fill with color based on game mode
        val color = when (map.mode) {
            com.example.brawldraft.data.GameMode.GEM_GRAB -> 0xFF00FF80.toInt()
            com.example.brawldraft.data.GameMode.BRAWL_BALL -> 0xFF0080FF.toInt()
            com.example.brawldraft.data.GameMode.BOUNTY -> 0xFFFFD700.toInt()
            com.example.brawldraft.data.GameMode.HEIST -> 0xFFFF4000.toInt()
            com.example.brawldraft.data.GameMode.SIEGE -> 0xFFFF0040.toInt()
            com.example.brawldraft.data.GameMode.HOT_ZONE -> 0xFFFF8000.toInt()
            com.example.brawldraft.data.GameMode.KNOCKOUT -> 0xFF8000FF.toInt()
            com.example.brawldraft.data.GameMode.SHOWDOWN -> 0xFF808080.toInt()
            com.example.brawldraft.data.GameMode.UNKNOWN -> 0xFF404040.toInt()
        }
        
        bitmap.eraseColor(color)
        return bitmap
    }
    
    suspend fun preloadBrawlerIcons() = withContext(Dispatchers.IO) {
        Log.d(TAG, "Preloading brawler icons...")
        val brawlers = BrawlerDatabase.getAllBrawlers()
        
        for (brawler in brawlers) {
            try {
                getBrawlerIcon(brawler)
            } catch (e: Exception) {
                Log.e(TAG, "Failed to preload icon for ${brawler.name}", e)
            }
        }
        
        Log.d(TAG, "Preloaded ${brawlers.size} brawler icons")
    }
    
    fun isAssetAvailable(assetPath: String): Boolean {
        return availableAssets.contains(assetPath)
    }
    
    fun getBrawlerIconPath(brawler: Brawler): String {
        return "$BRAWLER_ICONS_PATH/${brawler.id}.png"
    }
    
    fun getMapIconPath(map: GameMap): String {
        return "$MAP_ICONS_PATH/${map.id}.png"
    }
    
    fun getTemplateIconPath(brawler: Brawler): String {
        return "$TEMPLATE_ICONS_PATH/${brawler.id}.png"
    }
    
    fun clearCache() {
        bitmapCache.evictAll()
        Log.d(TAG, "Asset cache cleared")
    }
    
    fun getCacheInfo(): String {
        return "Cache: ${bitmapCache.size()}/${bitmapCache.maxSize()}, " +
                "Assets: ${availableAssets.size}"
    }
}
