<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="item_recommendation" modulePackage="com.example.brawldraft" filePath="app\src\main\res\layout\item_recommendation.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="android.widget.LinearLayout"><Targets><Target tag="layout/item_recommendation_0" view="LinearLayout"><Expressions/><location startLine="1" startOffset="0" endLine="62" endOffset="14"/></Target><Target id="@+id/priority_indicator" view="View"><Expressions/><location startLine="10" startOffset="4" endLine="15" endOffset="40"/></Target><Target id="@+id/brawler_name" view="TextView"><Expressions/><location startLine="30" startOffset="12" endLine="38" endOffset="42"/></Target><Target id="@+id/score_text" view="TextView"><Expressions/><location startLine="40" startOffset="12" endLine="47" endOffset="42"/></Target><Target id="@+id/reason_text" view="TextView"><Expressions/><location startLine="51" startOffset="8" endLine="58" endOffset="44"/></Target></Targets></Layout>