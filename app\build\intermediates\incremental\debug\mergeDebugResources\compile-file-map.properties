#Tue Aug 19 20:33:08 EEST 2025
com.example.brawldraft.app-main-33\:/mipmap-mdpi/ic_launcher_round.webp=C\:\\Users\\sukar\\AndroidStudioProjects\\Brawldraft\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\mipmap-mdpi_ic_launcher_round.webp.flat
com.example.brawldraft.app-main-33\:/drawable/ic_launcher_background.xml=C\:\\Users\\sukar\\AndroidStudioProjects\\Brawldraft\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_launcher_background.xml.flat
com.example.brawldraft.app-main-33\:/mipmap-mdpi/ic_launcher.webp=C\:\\Users\\sukar\\AndroidStudioProjects\\Brawldraft\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\mipmap-mdpi_ic_launcher.webp.flat
com.example.brawldraft.app-main-33\:/drawable/bg_circle.xml=C\:\\Users\\sukar\\AndroidStudioProjects\\Brawldraft\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_bg_circle.xml.flat
com.example.brawldraft.app-main-33\:/mipmap-anydpi-v26/ic_launcher.xml=C\:\\Users\\sukar\\AndroidStudioProjects\\Brawldraft\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\mipmap-anydpi-v26_ic_launcher.xml.flat
com.example.brawldraft.app-main-33\:/mipmap-xxxhdpi/ic_launcher_round.webp=C\:\\Users\\sukar\\AndroidStudioProjects\\Brawldraft\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\mipmap-xxxhdpi_ic_launcher_round.webp.flat
com.example.brawldraft.app-mergeDebugResources-30\:/layout/activity_main.xml=C\:\\Users\\sukar\\AndroidStudioProjects\\Brawldraft\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_activity_main.xml.flat
com.example.brawldraft.app-main-33\:/mipmap-xhdpi/ic_launcher.webp=C\:\\Users\\sukar\\AndroidStudioProjects\\Brawldraft\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\mipmap-xhdpi_ic_launcher.webp.flat
com.example.brawldraft.app-main-33\:/mipmap-xxxhdpi/ic_launcher.webp=C\:\\Users\\sukar\\AndroidStudioProjects\\Brawldraft\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\mipmap-xxxhdpi_ic_launcher.webp.flat
com.example.brawldraft.app-main-33\:/mipmap-xhdpi/ic_launcher_round.webp=C\:\\Users\\sukar\\AndroidStudioProjects\\Brawldraft\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\mipmap-xhdpi_ic_launcher_round.webp.flat
com.example.brawldraft.app-main-33\:/mipmap-xxhdpi/ic_launcher_round.webp=C\:\\Users\\sukar\\AndroidStudioProjects\\Brawldraft\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\mipmap-xxhdpi_ic_launcher_round.webp.flat
com.example.brawldraft.app-main-33\:/drawable/ic_launcher_foreground.xml=C\:\\Users\\sukar\\AndroidStudioProjects\\Brawldraft\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_launcher_foreground.xml.flat
com.example.brawldraft.app-main-33\:/xml/data_extraction_rules.xml=C\:\\Users\\sukar\\AndroidStudioProjects\\Brawldraft\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\xml_data_extraction_rules.xml.flat
com.example.brawldraft.app-main-33\:/mipmap-anydpi-v26/ic_launcher_round.xml=C\:\\Users\\sukar\\AndroidStudioProjects\\Brawldraft\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\mipmap-anydpi-v26_ic_launcher_round.xml.flat
com.example.brawldraft.app-main-33\:/mipmap-hdpi/ic_launcher.webp=C\:\\Users\\sukar\\AndroidStudioProjects\\Brawldraft\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\mipmap-hdpi_ic_launcher.webp.flat
com.example.brawldraft.app-main-33\:/mipmap-xxhdpi/ic_launcher.webp=C\:\\Users\\sukar\\AndroidStudioProjects\\Brawldraft\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\mipmap-xxhdpi_ic_launcher.webp.flat
com.example.brawldraft.app-main-33\:/mipmap-hdpi/ic_launcher_round.webp=C\:\\Users\\sukar\\AndroidStudioProjects\\Brawldraft\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\mipmap-hdpi_ic_launcher_round.webp.flat
com.example.brawldraft.app-main-33\:/xml/backup_rules.xml=C\:\\Users\\sukar\\AndroidStudioProjects\\Brawldraft\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\xml_backup_rules.xml.flat
com.example.brawldraft.app-mergeDebugResources-30\:/layout/floating_overlay.xml=C\:\\Users\\sukar\\AndroidStudioProjects\\Brawldraft\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_floating_overlay.xml.flat
com.example.brawldraft.app-mergeDebugResources-30\:/layout/overlay_collapsed.xml=C\:\\Users\\sukar\\AndroidStudioProjects\\Brawldraft\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_overlay_collapsed.xml.flat
com.example.brawldraft.app-mergeDebugResources-30\:/layout/overlay_expanded.xml=C\:\\Users\\sukar\\AndroidStudioProjects\\Brawldraft\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_overlay_expanded.xml.flat
com.example.brawldraft.app-main-33\:/drawable/bg_overlay_expanded.xml=C\:\\Users\\sukar\\AndroidStudioProjects\\Brawldraft\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_bg_overlay_expanded.xml.flat
