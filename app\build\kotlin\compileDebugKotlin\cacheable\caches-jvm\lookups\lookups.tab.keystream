  ic_menu_camera android.R.drawable  SuppressLint android.annotation  Activity android.app  Notification android.app  NotificationChannel android.app  NotificationManager android.app  Service android.app  ActivityResultContracts android.app.Activity  Build android.app.Activity  Button android.app.Activity  FloatingOverlayService android.app.Activity  Intent android.app.Activity  Log android.app.Activity  OpenCVLoader android.app.Activity  R android.app.Activity  REQUEST_SCREEN_CAPTURE android.app.Activity  RESULT_CANCELED android.app.Activity  	RESULT_OK android.app.Activity  Settings android.app.Activity  TAG android.app.Activity  Toast android.app.Activity  Uri android.app.Activity  apply android.app.Activity  finish android.app.Activity  intent android.app.Activity  java android.app.Activity  onActivityResult android.app.Activity  onCreate android.app.Activity  startActivityForResult android.app.Activity  apply android.app.NotificationChannel  description android.app.NotificationChannel  setShowBadge android.app.NotificationChannel  IMPORTANCE_LOW android.app.NotificationManager  createNotificationChannel android.app.NotificationManager  ACTION_START_CAPTURE android.app.Service  ACTION_STOP_CAPTURE android.app.Service  Activity android.app.Service  Bitmap android.app.Service  Boolean android.app.Service  BroadcastReceiver android.app.Service  Build android.app.Service  Button android.app.Service  CAPTURE_INTERVAL_MS android.app.Service  
CHANNEL_ID android.app.Service  Context android.app.Service  CoroutineScope android.app.Service  Dispatchers android.app.Service  DisplayManager android.app.Service  DisplayMetrics android.app.Service  
DraftPhase android.app.Service  DraftStateManager android.app.Service  EXTRA_RESULT_CODE android.app.Service  EXTRA_RESULT_DATA android.app.Service  	Exception android.app.Service  Float android.app.Service  FrameLayout android.app.Service  Gravity android.app.Service  Handler android.app.Service  Image android.app.Service  
ImageAnalyzer android.app.Service  ImageReader android.app.Service  Int android.app.Service  Intent android.app.Service  IntentFilter android.app.Service  LayoutInflater android.app.Service  LinearLayoutManager android.app.Service  Log android.app.Service  Looper android.app.Service  MEDIA_PROJECTION_SERVICE android.app.Service  MediaProjectionManager android.app.Service  MotionEvent android.app.Service  NOTIFICATION_ID android.app.Service  NotificationChannel android.app.Service  NotificationCompat android.app.Service  NotificationManager android.app.Service  PixelFormat android.app.Service  R android.app.Service  RecommendationAdapter android.app.Service  START_NOT_STICKY android.app.Service  ScreenCaptureManager android.app.Service  ScreenCapturePermissionActivity android.app.Service  
SupervisorJob android.app.Service  Suppress android.app.Service  TAG android.app.Service  TemplateMatcher android.app.Service  TextView android.app.Service  Toast android.app.Service  View android.app.Service  WINDOW_SERVICE android.app.Service  
WindowManager android.app.Service  android android.app.Service  apply android.app.Service  buildString android.app.Service  cancel android.app.Service  com android.app.Service  delay android.app.Service  draftStateManager android.app.Service  floatingView android.app.Service  getBrawlerByName android.app.Service  handleAnalysisResult android.app.Service  
imageAnalyzer android.app.Service  isActive android.app.Service  
isInitialized android.app.Service  
isNotEmpty android.app.Service  java android.app.Service  launch android.app.Service  let android.app.Service  onCreate android.app.Service  	onDestroy android.app.Service  params android.app.Service  processCapturedBitmap android.app.Service  
sendBroadcast android.app.Service  startForeground android.app.Service  startScreenCapture android.app.Service  stopSelf android.app.Service  updateDraftStatusUI android.app.Service  updateDraftTeamsUI android.app.Service  updateRecommendationsUI android.app.Service  
windowManager android.app.Service  withContext android.app.Service  OnTouchListener android.app.Service.View  BroadcastReceiver android.content  
ComponentName android.content  Context android.content  
ContextParams android.content  Intent android.content  IntentFilter android.content  Activity !android.content.BroadcastReceiver  Intent !android.content.BroadcastReceiver  handleAnalysisResult !android.content.BroadcastReceiver  startScreenCapture !android.content.BroadcastReceiver  ACTION_START_CAPTURE android.content.Context  ACTION_STOP_CAPTURE android.content.Context  Activity android.content.Context  ActivityResultContracts android.content.Context  Bitmap android.content.Context  Boolean android.content.Context  BroadcastReceiver android.content.Context  Build android.content.Context  Button android.content.Context  CAPTURE_INTERVAL_MS android.content.Context  
CHANNEL_ID android.content.Context  Context android.content.Context  CoroutineScope android.content.Context  Dispatchers android.content.Context  DisplayManager android.content.Context  DisplayMetrics android.content.Context  
DraftPhase android.content.Context  DraftStateManager android.content.Context  EXTRA_RESULT_CODE android.content.Context  EXTRA_RESULT_DATA android.content.Context  	Exception android.content.Context  Float android.content.Context  FloatingOverlayService android.content.Context  FrameLayout android.content.Context  Gravity android.content.Context  Handler android.content.Context  Image android.content.Context  
ImageAnalyzer android.content.Context  ImageReader android.content.Context  Int android.content.Context  Intent android.content.Context  IntentFilter android.content.Context  LayoutInflater android.content.Context  LinearLayoutManager android.content.Context  Log android.content.Context  Looper android.content.Context  MEDIA_PROJECTION_SERVICE android.content.Context  MediaProjectionManager android.content.Context  MotionEvent android.content.Context  NOTIFICATION_ID android.content.Context  NotificationChannel android.content.Context  NotificationCompat android.content.Context  NotificationManager android.content.Context  OpenCVLoader android.content.Context  PixelFormat android.content.Context  R android.content.Context  REQUEST_SCREEN_CAPTURE android.content.Context  RecommendationAdapter android.content.Context  START_NOT_STICKY android.content.Context  ScreenCaptureManager android.content.Context  ScreenCapturePermissionActivity android.content.Context  Settings android.content.Context  
SupervisorJob android.content.Context  Suppress android.content.Context  TAG android.content.Context  TemplateMatcher android.content.Context  TextView android.content.Context  Toast android.content.Context  Uri android.content.Context  View android.content.Context  WINDOW_SERVICE android.content.Context  
WindowManager android.content.Context  android android.content.Context  apply android.content.Context  assets android.content.Context  buildString android.content.Context  cancel android.content.Context  com android.content.Context  delay android.content.Context  draftStateManager android.content.Context  filesDir android.content.Context  floatingView android.content.Context  getBrawlerByName android.content.Context  getColor android.content.Context  getColorStateList android.content.Context  getSystemService android.content.Context  handleAnalysisResult android.content.Context  
imageAnalyzer android.content.Context  isActive android.content.Context  
isInitialized android.content.Context  
isNotEmpty android.content.Context  java android.content.Context  launch android.content.Context  let android.content.Context  params android.content.Context  processCapturedBitmap android.content.Context  
sendBroadcast android.content.Context  startForegroundService android.content.Context  startScreenCapture android.content.Context  startService android.content.Context  updateDraftStatusUI android.content.Context  updateDraftTeamsUI android.content.Context  updateRecommendationsUI android.content.Context  
windowManager android.content.Context  withContext android.content.Context  OnTouchListener android.content.Context.View  ACTION_START_CAPTURE android.content.ContextWrapper  ACTION_STOP_CAPTURE android.content.ContextWrapper  Activity android.content.ContextWrapper  ActivityResultContracts android.content.ContextWrapper  Bitmap android.content.ContextWrapper  Boolean android.content.ContextWrapper  BroadcastReceiver android.content.ContextWrapper  Build android.content.ContextWrapper  Button android.content.ContextWrapper  CAPTURE_INTERVAL_MS android.content.ContextWrapper  
CHANNEL_ID android.content.ContextWrapper  Context android.content.ContextWrapper  CoroutineScope android.content.ContextWrapper  Dispatchers android.content.ContextWrapper  DisplayManager android.content.ContextWrapper  DisplayMetrics android.content.ContextWrapper  
DraftPhase android.content.ContextWrapper  DraftStateManager android.content.ContextWrapper  EXTRA_RESULT_CODE android.content.ContextWrapper  EXTRA_RESULT_DATA android.content.ContextWrapper  	Exception android.content.ContextWrapper  Float android.content.ContextWrapper  FloatingOverlayService android.content.ContextWrapper  FrameLayout android.content.ContextWrapper  Gravity android.content.ContextWrapper  Handler android.content.ContextWrapper  Image android.content.ContextWrapper  
ImageAnalyzer android.content.ContextWrapper  ImageReader android.content.ContextWrapper  Int android.content.ContextWrapper  Intent android.content.ContextWrapper  IntentFilter android.content.ContextWrapper  LayoutInflater android.content.ContextWrapper  LinearLayoutManager android.content.ContextWrapper  Log android.content.ContextWrapper  Looper android.content.ContextWrapper  MEDIA_PROJECTION_SERVICE android.content.ContextWrapper  MediaProjectionManager android.content.ContextWrapper  MotionEvent android.content.ContextWrapper  NOTIFICATION_ID android.content.ContextWrapper  NotificationChannel android.content.ContextWrapper  NotificationCompat android.content.ContextWrapper  NotificationManager android.content.ContextWrapper  OpenCVLoader android.content.ContextWrapper  PixelFormat android.content.ContextWrapper  R android.content.ContextWrapper  REQUEST_SCREEN_CAPTURE android.content.ContextWrapper  RecommendationAdapter android.content.ContextWrapper  START_NOT_STICKY android.content.ContextWrapper  ScreenCaptureManager android.content.ContextWrapper  ScreenCapturePermissionActivity android.content.ContextWrapper  Settings android.content.ContextWrapper  
SupervisorJob android.content.ContextWrapper  Suppress android.content.ContextWrapper  TAG android.content.ContextWrapper  TemplateMatcher android.content.ContextWrapper  TextView android.content.ContextWrapper  Toast android.content.ContextWrapper  Uri android.content.ContextWrapper  View android.content.ContextWrapper  WINDOW_SERVICE android.content.ContextWrapper  
WindowManager android.content.ContextWrapper  android android.content.ContextWrapper  apply android.content.ContextWrapper  buildString android.content.ContextWrapper  cancel android.content.ContextWrapper  com android.content.ContextWrapper  delay android.content.ContextWrapper  draftStateManager android.content.ContextWrapper  floatingView android.content.ContextWrapper  getBrawlerByName android.content.ContextWrapper  getSystemService android.content.ContextWrapper  handleAnalysisResult android.content.ContextWrapper  
imageAnalyzer android.content.ContextWrapper  isActive android.content.ContextWrapper  
isInitialized android.content.ContextWrapper  
isNotEmpty android.content.ContextWrapper  java android.content.ContextWrapper  launch android.content.ContextWrapper  let android.content.ContextWrapper  packageName android.content.ContextWrapper  params android.content.ContextWrapper  processCapturedBitmap android.content.ContextWrapper  registerReceiver android.content.ContextWrapper  
sendBroadcast android.content.ContextWrapper  
startActivity android.content.ContextWrapper  startScreenCapture android.content.ContextWrapper  startService android.content.ContextWrapper  unregisterReceiver android.content.ContextWrapper  updateDraftStatusUI android.content.ContextWrapper  updateDraftTeamsUI android.content.ContextWrapper  updateRecommendationsUI android.content.ContextWrapper  
windowManager android.content.ContextWrapper  withContext android.content.ContextWrapper  OnTouchListener #android.content.ContextWrapper.View  FLAG_ACTIVITY_NEW_TASK android.content.Intent  Intent android.content.Intent  ScreenCaptureService android.content.Intent  action android.content.Intent  addFlags android.content.Intent  apply android.content.Intent  
getFloatExtra android.content.Intent  getIntExtra android.content.Intent  getParcelableExtra android.content.Intent  getStringExtra android.content.Intent  let android.content.Intent  putExtra android.content.Intent  ColorStateList android.content.res  list  android.content.res.AssetManager  open  android.content.res.AssetManager  Bitmap android.graphics  
BitmapFactory android.graphics  Brawler android.graphics  BrawlerDatabase android.graphics  Canvas android.graphics  Color android.graphics  Context android.graphics  Dispatchers android.graphics  	Exception android.graphics  File android.graphics  FileOutputStream android.graphics  	ICON_SIZE android.graphics  Paint android.graphics  PixelFormat android.graphics  	TEXT_SIZE android.graphics  Typeface android.graphics  apply android.graphics  com android.graphics  context android.graphics  generateBrawlerIcon android.graphics  getAllBrawlers android.graphics  withContext android.graphics  CompressFormat android.graphics.Bitmap  Config android.graphics.Bitmap  compress android.graphics.Bitmap  copyPixelsFromBuffer android.graphics.Bitmap  createBitmap android.graphics.Bitmap  
eraseColor android.graphics.Bitmap  height android.graphics.Bitmap  let android.graphics.Bitmap  recycle android.graphics.Bitmap  width android.graphics.Bitmap  PNG &android.graphics.Bitmap.CompressFormat  	ARGB_8888 android.graphics.Bitmap.Config  decodeStream android.graphics.BitmapFactory  	drawColor android.graphics.Canvas  drawRect android.graphics.Canvas  drawText android.graphics.Canvas  BLACK android.graphics.Color  WHITE android.graphics.Color  YELLOW android.graphics.Color  
parseColor android.graphics.Color  Align android.graphics.Paint  Color android.graphics.Paint  Paint android.graphics.Paint  Style android.graphics.Paint  	TEXT_SIZE android.graphics.Paint  Typeface android.graphics.Paint  apply android.graphics.Paint  color android.graphics.Paint  isAntiAlias android.graphics.Paint  strokeWidth android.graphics.Paint  style android.graphics.Paint  	textAlign android.graphics.Paint  textSize android.graphics.Paint  typeface android.graphics.Paint  CENTER android.graphics.Paint.Align  STROKE android.graphics.Paint.Style  	RGBA_8888 android.graphics.PixelFormat  TRANSLUCENT android.graphics.PixelFormat  DEFAULT_BOLD android.graphics.Typeface  DisplayManager android.hardware.display  VirtualDisplay android.hardware.display   VIRTUAL_DISPLAY_FLAG_AUTO_MIRROR 'android.hardware.display.DisplayManager  release 'android.hardware.display.VirtualDisplay  Image 
android.media  ImageReader 
android.media  close android.media.Image  planes android.media.Image  buffer android.media.Image.Plane  pixelStride android.media.Image.Plane  	rowStride android.media.Image.Plane  OnImageAvailableListener android.media.ImageReader  acquireLatestImage android.media.ImageReader  close android.media.ImageReader  newInstance android.media.ImageReader  setOnImageAvailableListener android.media.ImageReader  surface android.media.ImageReader  <SAM-CONSTRUCTOR> 2android.media.ImageReader.OnImageAvailableListener  MediaProjection android.media.projection  MediaProjectionManager android.media.projection  createVirtualDisplay (android.media.projection.MediaProjection  stop (android.media.projection.MediaProjection  createScreenCaptureIntent /android.media.projection.MediaProjectionManager  getMediaProjection /android.media.projection.MediaProjectionManager  Uri android.net  parse android.net.Uri  Build 
android.os  Bundle 
android.os  Handler 
android.os  IBinder 
android.os  Looper 
android.os  SDK_INT android.os.Build.VERSION  M android.os.Build.VERSION_CODES  O android.os.Build.VERSION_CODES  R android.os.Build.VERSION_CODES  post android.os.Handler  
getMainLooper android.os.Looper  Settings android.provider   ACTION_MANAGE_OVERLAY_PERMISSION android.provider.Settings  canDrawOverlays android.provider.Settings  DisplayMetrics android.util  Log android.util  LruCache android.util  
densityDpi android.util.DisplayMetrics  heightPixels android.util.DisplayMetrics  widthPixels android.util.DisplayMetrics  d android.util.Log  e android.util.Log  w android.util.Log  evictAll android.util.LruCache  get android.util.LruCache  maxSize android.util.LruCache  put android.util.LruCache  size android.util.LruCache  Gravity android.view  LayoutInflater android.view  MotionEvent android.view  Surface android.view  View android.view  	ViewGroup android.view  
WindowManager android.view  ActivityResultContracts  android.view.ContextThemeWrapper  Build  android.view.ContextThemeWrapper  Button  android.view.ContextThemeWrapper  FloatingOverlayService  android.view.ContextThemeWrapper  Intent  android.view.ContextThemeWrapper  Log  android.view.ContextThemeWrapper  OpenCVLoader  android.view.ContextThemeWrapper  R  android.view.ContextThemeWrapper  REQUEST_SCREEN_CAPTURE  android.view.ContextThemeWrapper  Settings  android.view.ContextThemeWrapper  TAG  android.view.ContextThemeWrapper  Toast  android.view.ContextThemeWrapper  Uri  android.view.ContextThemeWrapper  apply  android.view.ContextThemeWrapper  java  android.view.ContextThemeWrapper  getRealMetrics android.view.Display  START android.view.Gravity  TOP android.view.Gravity  from android.view.LayoutInflater  inflate android.view.LayoutInflater  ACTION_DOWN android.view.MotionEvent  ACTION_MOVE android.view.MotionEvent  	ACTION_UP android.view.MotionEvent  action android.view.MotionEvent  rawX android.view.MotionEvent  rawY android.view.MotionEvent  GONE android.view.View  OnClickListener android.view.View  OnLongClickListener android.view.View  OnTouchListener android.view.View  VISIBLE android.view.View  backgroundTintList android.view.View  context android.view.View  findViewById android.view.View  setBackgroundColor android.view.View  setOnClickListener android.view.View  setOnLongClickListener android.view.View  setOnTouchListener android.view.View  
visibility android.view.View  <SAM-CONSTRUCTOR> !android.view.View.OnClickListener  <SAM-CONSTRUCTOR> %android.view.View.OnLongClickListener  addView android.view.ViewGroup  context android.view.ViewGroup  removeAllViews android.view.ViewGroup  WRAP_CONTENT #android.view.ViewGroup.LayoutParams  addView android.view.ViewManager  
removeView android.view.ViewManager  updateViewLayout android.view.ViewManager  LayoutParams android.view.WindowManager  addView android.view.WindowManager  defaultDisplay android.view.WindowManager  
removeView android.view.WindowManager  updateViewLayout android.view.WindowManager  FLAG_NOT_FOCUSABLE 'android.view.WindowManager.LayoutParams  TYPE_APPLICATION_OVERLAY 'android.view.WindowManager.LayoutParams  
TYPE_PHONE 'android.view.WindowManager.LayoutParams  WRAP_CONTENT 'android.view.WindowManager.LayoutParams  gravity 'android.view.WindowManager.LayoutParams  x 'android.view.WindowManager.LayoutParams  y 'android.view.WindowManager.LayoutParams  Button android.widget  FrameLayout android.widget  LinearLayout android.widget  TextView android.widget  Toast android.widget  backgroundTintList android.widget.Button  setOnClickListener android.widget.Button  text android.widget.Button  addView android.widget.FrameLayout  setOnTouchListener android.widget.FrameLayout  addView android.widget.LinearLayout  removeAllViews android.widget.LinearLayout  
visibility android.widget.LinearLayout  text android.widget.TextView  LENGTH_LONG android.widget.Toast  LENGTH_SHORT android.widget.Toast  makeText android.widget.Toast  show android.widget.Toast  ActivityResultContracts #androidx.activity.ComponentActivity  Build #androidx.activity.ComponentActivity  Button #androidx.activity.ComponentActivity  FloatingOverlayService #androidx.activity.ComponentActivity  Intent #androidx.activity.ComponentActivity  OpenCVLoader #androidx.activity.ComponentActivity  R #androidx.activity.ComponentActivity  Settings #androidx.activity.ComponentActivity  Toast #androidx.activity.ComponentActivity  Uri #androidx.activity.ComponentActivity  java #androidx.activity.ComponentActivity  registerForActivityResult #androidx.activity.ComponentActivity  ActivityResultCallback androidx.activity.result  ActivityResultLauncher androidx.activity.result  <SAM-CONSTRUCTOR> /androidx.activity.result.ActivityResultCallback  launch /androidx.activity.result.ActivityResultLauncher  ActivityResultContracts !androidx.activity.result.contract  StartActivityForResult 9androidx.activity.result.contract.ActivityResultContracts  AppCompatActivity androidx.appcompat.app  ActivityResultContracts (androidx.appcompat.app.AppCompatActivity  Build (androidx.appcompat.app.AppCompatActivity  Button (androidx.appcompat.app.AppCompatActivity  FloatingOverlayService (androidx.appcompat.app.AppCompatActivity  Intent (androidx.appcompat.app.AppCompatActivity  OpenCVLoader (androidx.appcompat.app.AppCompatActivity  R (androidx.appcompat.app.AppCompatActivity  Settings (androidx.appcompat.app.AppCompatActivity  Toast (androidx.appcompat.app.AppCompatActivity  Uri (androidx.appcompat.app.AppCompatActivity  findViewById (androidx.appcompat.app.AppCompatActivity  java (androidx.appcompat.app.AppCompatActivity  onCreate (androidx.appcompat.app.AppCompatActivity  setContentView (androidx.appcompat.app.AppCompatActivity  NotificationCompat androidx.core.app  ActivityResultContracts #androidx.core.app.ComponentActivity  Build #androidx.core.app.ComponentActivity  Button #androidx.core.app.ComponentActivity  FloatingOverlayService #androidx.core.app.ComponentActivity  Intent #androidx.core.app.ComponentActivity  OpenCVLoader #androidx.core.app.ComponentActivity  R #androidx.core.app.ComponentActivity  Settings #androidx.core.app.ComponentActivity  Toast #androidx.core.app.ComponentActivity  Uri #androidx.core.app.ComponentActivity  java #androidx.core.app.ComponentActivity  Builder $androidx.core.app.NotificationCompat  PRIORITY_LOW $androidx.core.app.NotificationCompat  build ,androidx.core.app.NotificationCompat.Builder  setContentText ,androidx.core.app.NotificationCompat.Builder  setContentTitle ,androidx.core.app.NotificationCompat.Builder  
setOngoing ,androidx.core.app.NotificationCompat.Builder  setPriority ,androidx.core.app.NotificationCompat.Builder  setSmallIcon ,androidx.core.app.NotificationCompat.Builder  
ContextCompat androidx.core.content  getColor #androidx.core.content.ContextCompat  ActivityResultContracts &androidx.fragment.app.FragmentActivity  Build &androidx.fragment.app.FragmentActivity  Button &androidx.fragment.app.FragmentActivity  FloatingOverlayService &androidx.fragment.app.FragmentActivity  Intent &androidx.fragment.app.FragmentActivity  OpenCVLoader &androidx.fragment.app.FragmentActivity  R &androidx.fragment.app.FragmentActivity  Settings &androidx.fragment.app.FragmentActivity  Toast &androidx.fragment.app.FragmentActivity  Uri &androidx.fragment.app.FragmentActivity  java &androidx.fragment.app.FragmentActivity  onCreate &androidx.fragment.app.FragmentActivity  DiffUtil androidx.recyclerview.widget  LinearLayoutManager androidx.recyclerview.widget  ListAdapter androidx.recyclerview.widget  RecyclerView androidx.recyclerview.widget  ItemCallback %androidx.recyclerview.widget.DiffUtil  
ContextCompat (androidx.recyclerview.widget.ListAdapter  LayoutInflater (androidx.recyclerview.widget.ListAdapter  R (androidx.recyclerview.widget.ListAdapter  RecommendationPriority (androidx.recyclerview.widget.ListAdapter  onRecommendationClick (androidx.recyclerview.widget.ListAdapter  
LayoutManager )androidx.recyclerview.widget.RecyclerView  
ViewHolder )androidx.recyclerview.widget.RecyclerView  adapter )androidx.recyclerview.widget.RecyclerView  
layoutManager )androidx.recyclerview.widget.RecyclerView  
ContextCompat 1androidx.recyclerview.widget.RecyclerView.Adapter  LayoutInflater 1androidx.recyclerview.widget.RecyclerView.Adapter  R 1androidx.recyclerview.widget.RecyclerView.Adapter  RecommendationPriority 1androidx.recyclerview.widget.RecyclerView.Adapter  onRecommendationClick 1androidx.recyclerview.widget.RecyclerView.Adapter  
ContextCompat 4androidx.recyclerview.widget.RecyclerView.ViewHolder  R 4androidx.recyclerview.widget.RecyclerView.ViewHolder  RecommendationPriority 4androidx.recyclerview.widget.RecyclerView.ViewHolder  itemView 4androidx.recyclerview.widget.RecyclerView.ViewHolder  onRecommendationClick 4androidx.recyclerview.widget.RecyclerView.ViewHolder  ACTION_START_CAPTURE com.example.brawldraft  ACTION_STOP_CAPTURE com.example.brawldraft  
ActionType com.example.brawldraft  Activity com.example.brawldraft  ActivityResultContracts com.example.brawldraft  AppCompatActivity com.example.brawldraft  Bitmap com.example.brawldraft  Boolean com.example.brawldraft  Brawler com.example.brawldraft  BrawlerDatabase com.example.brawldraft  BrawlerRecommendation com.example.brawldraft  BrawlerType com.example.brawldraft  BroadcastReceiver com.example.brawldraft  Build com.example.brawldraft  Bundle com.example.brawldraft  Button com.example.brawldraft  CAPTURE_INTERVAL_MS com.example.brawldraft  
CHANNEL_ID com.example.brawldraft  Context com.example.brawldraft  CoroutineScope com.example.brawldraft  Dispatchers com.example.brawldraft  DisplayManager com.example.brawldraft  DisplayMetrics com.example.brawldraft  DraftAction com.example.brawldraft  
DraftPhase com.example.brawldraft  
DraftState com.example.brawldraft  DraftStateManager com.example.brawldraft  DraftStateUpdate com.example.brawldraft  EXTRA_RESULT_CODE com.example.brawldraft  EXTRA_RESULT_DATA com.example.brawldraft  	Exception com.example.brawldraft  Float com.example.brawldraft  FloatingOverlayService com.example.brawldraft  FrameLayout com.example.brawldraft  GameMap com.example.brawldraft  GameMode com.example.brawldraft  Gravity com.example.brawldraft  Handler com.example.brawldraft  IBinder com.example.brawldraft  Image com.example.brawldraft  
ImageAnalyzer com.example.brawldraft  ImageReader com.example.brawldraft  Int com.example.brawldraft  Intent com.example.brawldraft  IntentFilter com.example.brawldraft  Job com.example.brawldraft  LayoutInflater com.example.brawldraft  LinearLayout com.example.brawldraft  LinearLayoutManager com.example.brawldraft  List com.example.brawldraft  Log com.example.brawldraft  Looper com.example.brawldraft  MAX_RECOMMENDATIONS com.example.brawldraft  MEDIA_PROJECTION_SERVICE com.example.brawldraft  MainActivity com.example.brawldraft  MediaProjection com.example.brawldraft  MediaProjectionManager com.example.brawldraft  MotionEvent com.example.brawldraft  MutableStateFlow com.example.brawldraft  NOTIFICATION_ID com.example.brawldraft  Notification com.example.brawldraft  NotificationChannel com.example.brawldraft  NotificationCompat com.example.brawldraft  NotificationManager com.example.brawldraft  OpenCVLoader com.example.brawldraft  PixelFormat com.example.brawldraft  R com.example.brawldraft  REQUEST_SCREEN_CAPTURE com.example.brawldraft  RecommendationAdapter com.example.brawldraft  RecommendationEngine com.example.brawldraft  RecommendationPriority com.example.brawldraft  RecyclerView com.example.brawldraft  START_NOT_STICKY com.example.brawldraft  ScreenCaptureManager com.example.brawldraft  ScreenCapturePermissionActivity com.example.brawldraft  ScreenCaptureService com.example.brawldraft  Service com.example.brawldraft  Settings com.example.brawldraft  	StateFlow com.example.brawldraft  String com.example.brawldraft  
SupervisorJob com.example.brawldraft  Suppress com.example.brawldraft  SuppressLint com.example.brawldraft  System com.example.brawldraft  TAG com.example.brawldraft  TeamSide com.example.brawldraft  	TeamState com.example.brawldraft  TemplateMatcher com.example.brawldraft  TextView com.example.brawldraft  Toast com.example.brawldraft  Uri com.example.brawldraft  View com.example.brawldraft  VirtualDisplay com.example.brawldraft  WINDOW_SERVICE com.example.brawldraft  
WindowManager com.example.brawldraft  android com.example.brawldraft  apply com.example.brawldraft  asStateFlow com.example.brawldraft  buildString com.example.brawldraft  cancel com.example.brawldraft  com com.example.brawldraft  delay com.example.brawldraft  draftStateManager com.example.brawldraft  	emptyList com.example.brawldraft  filter com.example.brawldraft  floatingView com.example.brawldraft  getAllBrawlers com.example.brawldraft  getBrawlerByName com.example.brawldraft  handleAnalysisResult com.example.brawldraft  
imageAnalyzer com.example.brawldraft  isActive com.example.brawldraft  
isInitialized com.example.brawldraft  
isNotEmpty com.example.brawldraft  java com.example.brawldraft  joinToString com.example.brawldraft  launch com.example.brawldraft  let com.example.brawldraft  	lowercase com.example.brawldraft  map com.example.brawldraft  min com.example.brawldraft  
mutableListOf com.example.brawldraft  params com.example.brawldraft  plus com.example.brawldraft  
plusAssign com.example.brawldraft  processCapturedBitmap com.example.brawldraft  replace com.example.brawldraft  
sendBroadcast com.example.brawldraft  sortedByDescending com.example.brawldraft  startScreenCapture com.example.brawldraft  take com.example.brawldraft  updateDraftStatusUI com.example.brawldraft  updateDraftTeamsUI com.example.brawldraft  updateRecommendationsUI com.example.brawldraft  
windowManager com.example.brawldraft  withContext com.example.brawldraft  brawler ,com.example.brawldraft.BrawlerRecommendation  priority ,com.example.brawldraft.BrawlerRecommendation  reason ,com.example.brawldraft.BrawlerRecommendation  score ,com.example.brawldraft.BrawlerRecommendation  
ActionType (com.example.brawldraft.DraftStateManager  Brawler (com.example.brawldraft.DraftStateManager  BrawlerDatabase (com.example.brawldraft.DraftStateManager  BrawlerRecommendation (com.example.brawldraft.DraftStateManager  DraftAction (com.example.brawldraft.DraftStateManager  
DraftPhase (com.example.brawldraft.DraftStateManager  
DraftState (com.example.brawldraft.DraftStateManager  DraftStateUpdate (com.example.brawldraft.DraftStateManager  GameMap (com.example.brawldraft.DraftStateManager  GameMode (com.example.brawldraft.DraftStateManager  List (com.example.brawldraft.DraftStateManager  Log (com.example.brawldraft.DraftStateManager  MutableStateFlow (com.example.brawldraft.DraftStateManager  RecommendationEngine (com.example.brawldraft.DraftStateManager  RecommendationPriority (com.example.brawldraft.DraftStateManager  	StateFlow (com.example.brawldraft.DraftStateManager  String (com.example.brawldraft.DraftStateManager  System (com.example.brawldraft.DraftStateManager  TAG (com.example.brawldraft.DraftStateManager  TeamSide (com.example.brawldraft.DraftStateManager  _draftState (com.example.brawldraft.DraftStateManager  _recommendations (com.example.brawldraft.DraftStateManager  advancePickPhase (com.example.brawldraft.DraftStateManager  asStateFlow (com.example.brawldraft.DraftStateManager  
draftState (com.example.brawldraft.DraftStateManager  	emptyList (com.example.brawldraft.DraftStateManager  filter (com.example.brawldraft.DraftStateManager  generateRecommendations (com.example.brawldraft.DraftStateManager  getAllBrawlers (com.example.brawldraft.DraftStateManager  
isNotEmpty (com.example.brawldraft.DraftStateManager  	lowercase (com.example.brawldraft.DraftStateManager  manualBrawlerAction (com.example.brawldraft.DraftStateManager  map (com.example.brawldraft.DraftStateManager  plus (com.example.brawldraft.DraftStateManager  recommendationEngine (com.example.brawldraft.DraftStateManager  recommendations (com.example.brawldraft.DraftStateManager  replace (com.example.brawldraft.DraftStateManager  sortedByDescending (com.example.brawldraft.DraftStateManager  take (com.example.brawldraft.DraftStateManager  updateDraftState (com.example.brawldraft.DraftStateManager  updateRecommendations (com.example.brawldraft.DraftStateManager  
ActionType 2com.example.brawldraft.DraftStateManager.Companion  BrawlerDatabase 2com.example.brawldraft.DraftStateManager.Companion  BrawlerRecommendation 2com.example.brawldraft.DraftStateManager.Companion  DraftAction 2com.example.brawldraft.DraftStateManager.Companion  
DraftPhase 2com.example.brawldraft.DraftStateManager.Companion  
DraftState 2com.example.brawldraft.DraftStateManager.Companion  DraftStateUpdate 2com.example.brawldraft.DraftStateManager.Companion  GameMap 2com.example.brawldraft.DraftStateManager.Companion  GameMode 2com.example.brawldraft.DraftStateManager.Companion  Log 2com.example.brawldraft.DraftStateManager.Companion  MutableStateFlow 2com.example.brawldraft.DraftStateManager.Companion  RecommendationEngine 2com.example.brawldraft.DraftStateManager.Companion  RecommendationPriority 2com.example.brawldraft.DraftStateManager.Companion  System 2com.example.brawldraft.DraftStateManager.Companion  TAG 2com.example.brawldraft.DraftStateManager.Companion  TeamSide 2com.example.brawldraft.DraftStateManager.Companion  asStateFlow 2com.example.brawldraft.DraftStateManager.Companion  	emptyList 2com.example.brawldraft.DraftStateManager.Companion  filter 2com.example.brawldraft.DraftStateManager.Companion  getAllBrawlers 2com.example.brawldraft.DraftStateManager.Companion  
isNotEmpty 2com.example.brawldraft.DraftStateManager.Companion  	lowercase 2com.example.brawldraft.DraftStateManager.Companion  map 2com.example.brawldraft.DraftStateManager.Companion  plus 2com.example.brawldraft.DraftStateManager.Companion  replace 2com.example.brawldraft.DraftStateManager.Companion  sortedByDescending 2com.example.brawldraft.DraftStateManager.Companion  take 2com.example.brawldraft.DraftStateManager.Companion  
BrawlerBanned 9com.example.brawldraft.DraftStateManager.DraftStateUpdate  
BrawlerPicked 9com.example.brawldraft.DraftStateManager.DraftStateUpdate  
DraftReset 9com.example.brawldraft.DraftStateManager.DraftStateUpdate  ManualCorrection 9com.example.brawldraft.DraftStateManager.DraftStateUpdate  MapDetected 9com.example.brawldraft.DraftStateManager.DraftStateUpdate  PhaseChanged 9com.example.brawldraft.DraftStateManager.DraftStateUpdate  
BrawlerBanned 'com.example.brawldraft.DraftStateUpdate  
BrawlerPicked 'com.example.brawldraft.DraftStateUpdate  
DraftReset 'com.example.brawldraft.DraftStateUpdate  ManualCorrection 'com.example.brawldraft.DraftStateUpdate  MapDetected 'com.example.brawldraft.DraftStateUpdate  PhaseChanged 'com.example.brawldraft.DraftStateUpdate  Activity -com.example.brawldraft.FloatingOverlayService  Build -com.example.brawldraft.FloatingOverlayService  CoroutineScope -com.example.brawldraft.FloatingOverlayService  Dispatchers -com.example.brawldraft.FloatingOverlayService  
DraftPhase -com.example.brawldraft.FloatingOverlayService  DraftStateManager -com.example.brawldraft.FloatingOverlayService  FrameLayout -com.example.brawldraft.FloatingOverlayService  Gravity -com.example.brawldraft.FloatingOverlayService  Intent -com.example.brawldraft.FloatingOverlayService  IntentFilter -com.example.brawldraft.FloatingOverlayService  LayoutInflater -com.example.brawldraft.FloatingOverlayService  LinearLayoutManager -com.example.brawldraft.FloatingOverlayService  MEDIA_PROJECTION_SERVICE -com.example.brawldraft.FloatingOverlayService  MotionEvent -com.example.brawldraft.FloatingOverlayService  PixelFormat -com.example.brawldraft.FloatingOverlayService  R -com.example.brawldraft.FloatingOverlayService  RecommendationAdapter -com.example.brawldraft.FloatingOverlayService  ScreenCaptureManager -com.example.brawldraft.FloatingOverlayService  ScreenCapturePermissionActivity -com.example.brawldraft.FloatingOverlayService  
SupervisorJob -com.example.brawldraft.FloatingOverlayService  Toast -com.example.brawldraft.FloatingOverlayService  View -com.example.brawldraft.FloatingOverlayService  WINDOW_SERVICE -com.example.brawldraft.FloatingOverlayService  
WindowManager -com.example.brawldraft.FloatingOverlayService  analysisReceiver -com.example.brawldraft.FloatingOverlayService  apply -com.example.brawldraft.FloatingOverlayService  blueTeamBrawlers -com.example.brawldraft.FloatingOverlayService  buildString -com.example.brawldraft.FloatingOverlayService  cancel -com.example.brawldraft.FloatingOverlayService  
collapsedView -com.example.brawldraft.FloatingOverlayService  com -com.example.brawldraft.FloatingOverlayService  createTeamBrawlerView -com.example.brawldraft.FloatingOverlayService  draftStateManager -com.example.brawldraft.FloatingOverlayService  draftStatusText -com.example.brawldraft.FloatingOverlayService  draftTeamsContainer -com.example.brawldraft.FloatingOverlayService  expandedView -com.example.brawldraft.FloatingOverlayService  floatingView -com.example.brawldraft.FloatingOverlayService  getBrawlerByName -com.example.brawldraft.FloatingOverlayService  getColor -com.example.brawldraft.FloatingOverlayService  getColorStateList -com.example.brawldraft.FloatingOverlayService  getSystemService -com.example.brawldraft.FloatingOverlayService  handleAnalysisResult -com.example.brawldraft.FloatingOverlayService  isCaptureActive -com.example.brawldraft.FloatingOverlayService  
isInitialized -com.example.brawldraft.FloatingOverlayService  
isNotEmpty -com.example.brawldraft.FloatingOverlayService  java -com.example.brawldraft.FloatingOverlayService  launch -com.example.brawldraft.FloatingOverlayService  manualOverrideContainer -com.example.brawldraft.FloatingOverlayService  params -com.example.brawldraft.FloatingOverlayService  recommendationAdapter -com.example.brawldraft.FloatingOverlayService  recommendationsContainer -com.example.brawldraft.FloatingOverlayService  recommendationsRecycler -com.example.brawldraft.FloatingOverlayService  redTeamBrawlers -com.example.brawldraft.FloatingOverlayService  registerReceiver -com.example.brawldraft.FloatingOverlayService  requestScreenCapturePermission -com.example.brawldraft.FloatingOverlayService  screenCaptureManager -com.example.brawldraft.FloatingOverlayService  screenCaptureReceiver -com.example.brawldraft.FloatingOverlayService  serviceScope -com.example.brawldraft.FloatingOverlayService  simulateTestDraft -com.example.brawldraft.FloatingOverlayService  
startActivity -com.example.brawldraft.FloatingOverlayService  startCaptureButton -com.example.brawldraft.FloatingOverlayService  startScreenCapture -com.example.brawldraft.FloatingOverlayService  stopScreenCapture -com.example.brawldraft.FloatingOverlayService  stopSelf -com.example.brawldraft.FloatingOverlayService  switchToCollapsedView -com.example.brawldraft.FloatingOverlayService  switchToExpandedView -com.example.brawldraft.FloatingOverlayService  toggleManualMode -com.example.brawldraft.FloatingOverlayService  unregisterReceiver -com.example.brawldraft.FloatingOverlayService  updateCaptureUI -com.example.brawldraft.FloatingOverlayService  updateDraftStatusUI -com.example.brawldraft.FloatingOverlayService  updateDraftTeamsUI -com.example.brawldraft.FloatingOverlayService  updateRecommendationsUI -com.example.brawldraft.FloatingOverlayService  updateTeamBrawlersUI -com.example.brawldraft.FloatingOverlayService  
windowManager -com.example.brawldraft.FloatingOverlayService  ActivityResultContracts #com.example.brawldraft.MainActivity  Build #com.example.brawldraft.MainActivity  FloatingOverlayService #com.example.brawldraft.MainActivity  Intent #com.example.brawldraft.MainActivity  OpenCVLoader #com.example.brawldraft.MainActivity  R #com.example.brawldraft.MainActivity  Settings #com.example.brawldraft.MainActivity  Toast #com.example.brawldraft.MainActivity  Uri #com.example.brawldraft.MainActivity  findViewById #com.example.brawldraft.MainActivity  isOverlayPermissionGranted #com.example.brawldraft.MainActivity  java #com.example.brawldraft.MainActivity  overlayPermissionLauncher #com.example.brawldraft.MainActivity  packageName #com.example.brawldraft.MainActivity  registerForActivityResult #com.example.brawldraft.MainActivity  requestOverlayPermission #com.example.brawldraft.MainActivity  setContentView #com.example.brawldraft.MainActivity  startOverlayService #com.example.brawldraft.MainActivity  startService #com.example.brawldraft.MainActivity  
statusText #com.example.brawldraft.MainActivity  
gaming_accent com.example.brawldraft.R.color  
gaming_danger com.example.brawldraft.R.color  gaming_success com.example.brawldraft.R.color  gaming_text_secondary com.example.brawldraft.R.color  gaming_warning com.example.brawldraft.R.color  action_type com.example.brawldraft.R.id  blue_team_brawlers com.example.brawldraft.R.id  brawler_name com.example.brawldraft.R.id  close_button com.example.brawldraft.R.id  draft_status com.example.brawldraft.R.id  draft_teams_container com.example.brawldraft.R.id  launch_overlay_button com.example.brawldraft.R.id  manual_ban_button com.example.brawldraft.R.id  manual_mode_button com.example.brawldraft.R.id  manual_override_container com.example.brawldraft.R.id  manual_pick_button com.example.brawldraft.R.id  minimize_button com.example.brawldraft.R.id  priority_indicator com.example.brawldraft.R.id  reason_text com.example.brawldraft.R.id  recommendations_container com.example.brawldraft.R.id  recommendations_recycler com.example.brawldraft.R.id  red_team_brawlers com.example.brawldraft.R.id  
score_text com.example.brawldraft.R.id  start_capture_button com.example.brawldraft.R.id  status_indicator com.example.brawldraft.R.id  status_text com.example.brawldraft.R.id  
activity_main com.example.brawldraft.R.layout  item_recommendation com.example.brawldraft.R.layout  item_team_brawler com.example.brawldraft.R.layout  overlay_collapsed com.example.brawldraft.R.layout  overlay_expanded com.example.brawldraft.R.layout  
ActionType +com.example.brawldraft.RecommendationEngine  Brawler +com.example.brawldraft.RecommendationEngine  BrawlerDatabase +com.example.brawldraft.RecommendationEngine  BrawlerRecommendation +com.example.brawldraft.RecommendationEngine  BrawlerType +com.example.brawldraft.RecommendationEngine  
DraftState +com.example.brawldraft.RecommendationEngine  Float +com.example.brawldraft.RecommendationEngine  GameMap +com.example.brawldraft.RecommendationEngine  GameMode +com.example.brawldraft.RecommendationEngine  List +com.example.brawldraft.RecommendationEngine  Log +com.example.brawldraft.RecommendationEngine  MAX_RECOMMENDATIONS +com.example.brawldraft.RecommendationEngine  RecommendationPriority +com.example.brawldraft.RecommendationEngine  String +com.example.brawldraft.RecommendationEngine  TAG +com.example.brawldraft.RecommendationEngine  	TeamState +com.example.brawldraft.RecommendationEngine  calculateBanScore +com.example.brawldraft.RecommendationEngine  calculateBountySynergy +com.example.brawldraft.RecommendationEngine  calculateBrawlBallSynergy +com.example.brawldraft.RecommendationEngine  calculateCounterPotential +com.example.brawldraft.RecommendationEngine  calculateCounterValue +com.example.brawldraft.RecommendationEngine  calculateGemGrabSynergy +com.example.brawldraft.RecommendationEngine  calculateHeistSynergy +com.example.brawldraft.RecommendationEngine  calculateHotZoneSynergy +com.example.brawldraft.RecommendationEngine  calculateKnockoutSynergy +com.example.brawldraft.RecommendationEngine  calculateMapSynergy +com.example.brawldraft.RecommendationEngine  calculatePickScore +com.example.brawldraft.RecommendationEngine  calculateShowdownSynergy +com.example.brawldraft.RecommendationEngine  calculateSiegeSynergy +com.example.brawldraft.RecommendationEngine  calculateTeamSynergy +com.example.brawldraft.RecommendationEngine  determinePriority +com.example.brawldraft.RecommendationEngine  filter +com.example.brawldraft.RecommendationEngine  generateBanReason +com.example.brawldraft.RecommendationEngine  generateBanRecommendations +com.example.brawldraft.RecommendationEngine  generatePickReason +com.example.brawldraft.RecommendationEngine  generatePickRecommendations +com.example.brawldraft.RecommendationEngine  generateRecommendations +com.example.brawldraft.RecommendationEngine  getAllBrawlers +com.example.brawldraft.RecommendationEngine  getAvailableBrawlers +com.example.brawldraft.RecommendationEngine  getCounterEffectiveness +com.example.brawldraft.RecommendationEngine  
isNotEmpty +com.example.brawldraft.RecommendationEngine  joinToString +com.example.brawldraft.RecommendationEngine  map +com.example.brawldraft.RecommendationEngine  min +com.example.brawldraft.RecommendationEngine  
mutableListOf +com.example.brawldraft.RecommendationEngine  
plusAssign +com.example.brawldraft.RecommendationEngine  sortedByDescending +com.example.brawldraft.RecommendationEngine  take +com.example.brawldraft.RecommendationEngine  
ActionType 5com.example.brawldraft.RecommendationEngine.Companion  BrawlerDatabase 5com.example.brawldraft.RecommendationEngine.Companion  BrawlerRecommendation 5com.example.brawldraft.RecommendationEngine.Companion  BrawlerType 5com.example.brawldraft.RecommendationEngine.Companion  GameMode 5com.example.brawldraft.RecommendationEngine.Companion  Log 5com.example.brawldraft.RecommendationEngine.Companion  MAX_RECOMMENDATIONS 5com.example.brawldraft.RecommendationEngine.Companion  RecommendationPriority 5com.example.brawldraft.RecommendationEngine.Companion  TAG 5com.example.brawldraft.RecommendationEngine.Companion  filter 5com.example.brawldraft.RecommendationEngine.Companion  getAllBrawlers 5com.example.brawldraft.RecommendationEngine.Companion  
isNotEmpty 5com.example.brawldraft.RecommendationEngine.Companion  joinToString 5com.example.brawldraft.RecommendationEngine.Companion  map 5com.example.brawldraft.RecommendationEngine.Companion  min 5com.example.brawldraft.RecommendationEngine.Companion  
mutableListOf 5com.example.brawldraft.RecommendationEngine.Companion  
plusAssign 5com.example.brawldraft.RecommendationEngine.Companion  sortedByDescending 5com.example.brawldraft.RecommendationEngine.Companion  take 5com.example.brawldraft.RecommendationEngine.Companion  CRITICAL -com.example.brawldraft.RecommendationPriority  HIGH -com.example.brawldraft.RecommendationPriority  LOW -com.example.brawldraft.RecommendationPriority  MEDIUM -com.example.brawldraft.RecommendationPriority  Activity +com.example.brawldraft.ScreenCaptureManager  Context +com.example.brawldraft.ScreenCaptureManager  Int +com.example.brawldraft.ScreenCaptureManager  Intent +com.example.brawldraft.ScreenCaptureManager  Log +com.example.brawldraft.ScreenCaptureManager  MediaProjectionManager +com.example.brawldraft.ScreenCaptureManager  REQUEST_SCREEN_CAPTURE +com.example.brawldraft.ScreenCaptureManager  ScreenCaptureService +com.example.brawldraft.ScreenCaptureManager  TAG +com.example.brawldraft.ScreenCaptureManager  apply +com.example.brawldraft.ScreenCaptureManager  context +com.example.brawldraft.ScreenCaptureManager  java +com.example.brawldraft.ScreenCaptureManager  mediaProjectionManager +com.example.brawldraft.ScreenCaptureManager  startScreenCapture +com.example.brawldraft.ScreenCaptureManager  stopScreenCapture +com.example.brawldraft.ScreenCaptureManager  Context 5com.example.brawldraft.ScreenCaptureManager.Companion  Intent 5com.example.brawldraft.ScreenCaptureManager.Companion  Log 5com.example.brawldraft.ScreenCaptureManager.Companion  REQUEST_SCREEN_CAPTURE 5com.example.brawldraft.ScreenCaptureManager.Companion  ScreenCaptureService 5com.example.brawldraft.ScreenCaptureManager.Companion  TAG 5com.example.brawldraft.ScreenCaptureManager.Companion  apply 5com.example.brawldraft.ScreenCaptureManager.Companion  java 5com.example.brawldraft.ScreenCaptureManager.Companion  Bundle 6com.example.brawldraft.ScreenCapturePermissionActivity  	Companion 6com.example.brawldraft.ScreenCapturePermissionActivity  Int 6com.example.brawldraft.ScreenCapturePermissionActivity  Intent 6com.example.brawldraft.ScreenCapturePermissionActivity  Log 6com.example.brawldraft.ScreenCapturePermissionActivity  REQUEST_SCREEN_CAPTURE 6com.example.brawldraft.ScreenCapturePermissionActivity  TAG 6com.example.brawldraft.ScreenCapturePermissionActivity  apply 6com.example.brawldraft.ScreenCapturePermissionActivity  finish 6com.example.brawldraft.ScreenCapturePermissionActivity  intent 6com.example.brawldraft.ScreenCapturePermissionActivity  
sendBroadcast 6com.example.brawldraft.ScreenCapturePermissionActivity  startActivityForResult 6com.example.brawldraft.ScreenCapturePermissionActivity  Intent @com.example.brawldraft.ScreenCapturePermissionActivity.Companion  Log @com.example.brawldraft.ScreenCapturePermissionActivity.Companion  REQUEST_SCREEN_CAPTURE @com.example.brawldraft.ScreenCapturePermissionActivity.Companion  TAG @com.example.brawldraft.ScreenCapturePermissionActivity.Companion  apply @com.example.brawldraft.ScreenCapturePermissionActivity.Companion  ACTION_START_CAPTURE +com.example.brawldraft.ScreenCaptureService  ACTION_STOP_CAPTURE +com.example.brawldraft.ScreenCaptureService  Bitmap +com.example.brawldraft.ScreenCaptureService  Build +com.example.brawldraft.ScreenCaptureService  CAPTURE_INTERVAL_MS +com.example.brawldraft.ScreenCaptureService  
CHANNEL_ID +com.example.brawldraft.ScreenCaptureService  	Companion +com.example.brawldraft.ScreenCaptureService  Context +com.example.brawldraft.ScreenCaptureService  CoroutineScope +com.example.brawldraft.ScreenCaptureService  Dispatchers +com.example.brawldraft.ScreenCaptureService  DisplayManager +com.example.brawldraft.ScreenCaptureService  DisplayMetrics +com.example.brawldraft.ScreenCaptureService  EXTRA_RESULT_CODE +com.example.brawldraft.ScreenCaptureService  EXTRA_RESULT_DATA +com.example.brawldraft.ScreenCaptureService  	Exception +com.example.brawldraft.ScreenCaptureService  Handler +com.example.brawldraft.ScreenCaptureService  IBinder +com.example.brawldraft.ScreenCaptureService  Image +com.example.brawldraft.ScreenCaptureService  
ImageAnalyzer +com.example.brawldraft.ScreenCaptureService  ImageReader +com.example.brawldraft.ScreenCaptureService  Int +com.example.brawldraft.ScreenCaptureService  Intent +com.example.brawldraft.ScreenCaptureService  Job +com.example.brawldraft.ScreenCaptureService  Log +com.example.brawldraft.ScreenCaptureService  Looper +com.example.brawldraft.ScreenCaptureService  MediaProjection +com.example.brawldraft.ScreenCaptureService  MediaProjectionManager +com.example.brawldraft.ScreenCaptureService  NOTIFICATION_ID +com.example.brawldraft.ScreenCaptureService  Notification +com.example.brawldraft.ScreenCaptureService  NotificationChannel +com.example.brawldraft.ScreenCaptureService  NotificationCompat +com.example.brawldraft.ScreenCaptureService  NotificationManager +com.example.brawldraft.ScreenCaptureService  PixelFormat +com.example.brawldraft.ScreenCaptureService  START_NOT_STICKY +com.example.brawldraft.ScreenCaptureService  
SupervisorJob +com.example.brawldraft.ScreenCaptureService  Suppress +com.example.brawldraft.ScreenCaptureService  TAG +com.example.brawldraft.ScreenCaptureService  TemplateMatcher +com.example.brawldraft.ScreenCaptureService  VirtualDisplay +com.example.brawldraft.ScreenCaptureService  
WindowManager +com.example.brawldraft.ScreenCaptureService  android +com.example.brawldraft.ScreenCaptureService  apply +com.example.brawldraft.ScreenCaptureService  cancel +com.example.brawldraft.ScreenCaptureService  captureHandler +com.example.brawldraft.ScreenCaptureService  
captureJob +com.example.brawldraft.ScreenCaptureService  createNotification +com.example.brawldraft.ScreenCaptureService  createNotificationChannel +com.example.brawldraft.ScreenCaptureService  delay +com.example.brawldraft.ScreenCaptureService  getScreenMetrics +com.example.brawldraft.ScreenCaptureService  getSystemService +com.example.brawldraft.ScreenCaptureService  
imageAnalyzer +com.example.brawldraft.ScreenCaptureService  imageReader +com.example.brawldraft.ScreenCaptureService  
imageToBitmap +com.example.brawldraft.ScreenCaptureService  isActive +com.example.brawldraft.ScreenCaptureService  java +com.example.brawldraft.ScreenCaptureService  launch +com.example.brawldraft.ScreenCaptureService  let +com.example.brawldraft.ScreenCaptureService  mediaProjection +com.example.brawldraft.ScreenCaptureService  mediaProjectionManager +com.example.brawldraft.ScreenCaptureService  processCapturedBitmap +com.example.brawldraft.ScreenCaptureService  processImage +com.example.brawldraft.ScreenCaptureService  
screenDensity +com.example.brawldraft.ScreenCaptureService  screenHeight +com.example.brawldraft.ScreenCaptureService  screenWidth +com.example.brawldraft.ScreenCaptureService  
sendBroadcast +com.example.brawldraft.ScreenCaptureService  serviceScope +com.example.brawldraft.ScreenCaptureService  startCapture +com.example.brawldraft.ScreenCaptureService  startForeground +com.example.brawldraft.ScreenCaptureService  startPeriodicCapture +com.example.brawldraft.ScreenCaptureService  stopCapture +com.example.brawldraft.ScreenCaptureService  stopSelf +com.example.brawldraft.ScreenCaptureService  templateMatcher +com.example.brawldraft.ScreenCaptureService  virtualDisplay +com.example.brawldraft.ScreenCaptureService  withContext +com.example.brawldraft.ScreenCaptureService  ACTION_START_CAPTURE 5com.example.brawldraft.ScreenCaptureService.Companion  ACTION_STOP_CAPTURE 5com.example.brawldraft.ScreenCaptureService.Companion  Bitmap 5com.example.brawldraft.ScreenCaptureService.Companion  Build 5com.example.brawldraft.ScreenCaptureService.Companion  CAPTURE_INTERVAL_MS 5com.example.brawldraft.ScreenCaptureService.Companion  
CHANNEL_ID 5com.example.brawldraft.ScreenCaptureService.Companion  Context 5com.example.brawldraft.ScreenCaptureService.Companion  CoroutineScope 5com.example.brawldraft.ScreenCaptureService.Companion  Dispatchers 5com.example.brawldraft.ScreenCaptureService.Companion  DisplayManager 5com.example.brawldraft.ScreenCaptureService.Companion  DisplayMetrics 5com.example.brawldraft.ScreenCaptureService.Companion  EXTRA_RESULT_CODE 5com.example.brawldraft.ScreenCaptureService.Companion  EXTRA_RESULT_DATA 5com.example.brawldraft.ScreenCaptureService.Companion  Handler 5com.example.brawldraft.ScreenCaptureService.Companion  
ImageAnalyzer 5com.example.brawldraft.ScreenCaptureService.Companion  ImageReader 5com.example.brawldraft.ScreenCaptureService.Companion  Log 5com.example.brawldraft.ScreenCaptureService.Companion  Looper 5com.example.brawldraft.ScreenCaptureService.Companion  NOTIFICATION_ID 5com.example.brawldraft.ScreenCaptureService.Companion  NotificationChannel 5com.example.brawldraft.ScreenCaptureService.Companion  NotificationCompat 5com.example.brawldraft.ScreenCaptureService.Companion  NotificationManager 5com.example.brawldraft.ScreenCaptureService.Companion  PixelFormat 5com.example.brawldraft.ScreenCaptureService.Companion  START_NOT_STICKY 5com.example.brawldraft.ScreenCaptureService.Companion  
SupervisorJob 5com.example.brawldraft.ScreenCaptureService.Companion  TAG 5com.example.brawldraft.ScreenCaptureService.Companion  TemplateMatcher 5com.example.brawldraft.ScreenCaptureService.Companion  android 5com.example.brawldraft.ScreenCaptureService.Companion  apply 5com.example.brawldraft.ScreenCaptureService.Companion  cancel 5com.example.brawldraft.ScreenCaptureService.Companion  delay 5com.example.brawldraft.ScreenCaptureService.Companion  
imageAnalyzer 5com.example.brawldraft.ScreenCaptureService.Companion  isActive 5com.example.brawldraft.ScreenCaptureService.Companion  java 5com.example.brawldraft.ScreenCaptureService.Companion  launch 5com.example.brawldraft.ScreenCaptureService.Companion  let 5com.example.brawldraft.ScreenCaptureService.Companion  processCapturedBitmap 5com.example.brawldraft.ScreenCaptureService.Companion  
sendBroadcast 5com.example.brawldraft.ScreenCaptureService.Companion  withContext 5com.example.brawldraft.ScreenCaptureService.Companion  OnTouchListener com.example.brawldraft.View  LayoutParams $com.example.brawldraft.WindowManager  AssetManager com.example.brawldraft.assets  BRAWLER_ICONS_PATH com.example.brawldraft.assets  Bitmap com.example.brawldraft.assets  
BitmapFactory com.example.brawldraft.assets  Boolean com.example.brawldraft.assets  Brawler com.example.brawldraft.assets  BrawlerDatabase com.example.brawldraft.assets  
CACHE_SIZE com.example.brawldraft.assets  Canvas com.example.brawldraft.assets  Color com.example.brawldraft.assets  Context com.example.brawldraft.assets  Dispatchers com.example.brawldraft.assets  	Exception com.example.brawldraft.assets  File com.example.brawldraft.assets  FileOutputStream com.example.brawldraft.assets  GameMap com.example.brawldraft.assets  	ICON_SIZE com.example.brawldraft.assets  IOException com.example.brawldraft.assets  Log com.example.brawldraft.assets  LruCache com.example.brawldraft.assets  MAP_ICONS_PATH com.example.brawldraft.assets  Paint com.example.brawldraft.assets  PlaceholderGenerator com.example.brawldraft.assets  String com.example.brawldraft.assets  TAG com.example.brawldraft.assets  TEMPLATE_ICONS_PATH com.example.brawldraft.assets  	TEXT_SIZE com.example.brawldraft.assets  Typeface com.example.brawldraft.assets  apply com.example.brawldraft.assets  availableAssets com.example.brawldraft.assets  bitmapCache com.example.brawldraft.assets  com com.example.brawldraft.assets  context com.example.brawldraft.assets  generateBrawlerIcon com.example.brawldraft.assets  generateBrawlerPlaceholder com.example.brawldraft.assets  generateMapPlaceholder com.example.brawldraft.assets  getAllBrawlers com.example.brawldraft.assets  getBrawlerIcon com.example.brawldraft.assets  let com.example.brawldraft.assets  loadBitmapFromAssets com.example.brawldraft.assets  mutableSetOf com.example.brawldraft.assets  scanAvailableAssets com.example.brawldraft.assets  withContext com.example.brawldraft.assets  BRAWLER_ICONS_PATH *com.example.brawldraft.assets.AssetManager  Bitmap *com.example.brawldraft.assets.AssetManager  
BitmapFactory *com.example.brawldraft.assets.AssetManager  Boolean *com.example.brawldraft.assets.AssetManager  Brawler *com.example.brawldraft.assets.AssetManager  BrawlerDatabase *com.example.brawldraft.assets.AssetManager  
CACHE_SIZE *com.example.brawldraft.assets.AssetManager  Context *com.example.brawldraft.assets.AssetManager  Dispatchers *com.example.brawldraft.assets.AssetManager  	Exception *com.example.brawldraft.assets.AssetManager  GameMap *com.example.brawldraft.assets.AssetManager  IOException *com.example.brawldraft.assets.AssetManager  Log *com.example.brawldraft.assets.AssetManager  LruCache *com.example.brawldraft.assets.AssetManager  MAP_ICONS_PATH *com.example.brawldraft.assets.AssetManager  String *com.example.brawldraft.assets.AssetManager  TAG *com.example.brawldraft.assets.AssetManager  TEMPLATE_ICONS_PATH *com.example.brawldraft.assets.AssetManager  
assetsScanned *com.example.brawldraft.assets.AssetManager  availableAssets *com.example.brawldraft.assets.AssetManager  bitmapCache *com.example.brawldraft.assets.AssetManager  com *com.example.brawldraft.assets.AssetManager  context *com.example.brawldraft.assets.AssetManager  generateBrawlerPlaceholder *com.example.brawldraft.assets.AssetManager  generateMapPlaceholder *com.example.brawldraft.assets.AssetManager  getAllBrawlers *com.example.brawldraft.assets.AssetManager  getBrawlerIcon *com.example.brawldraft.assets.AssetManager  let *com.example.brawldraft.assets.AssetManager  loadBitmapFromAssets *com.example.brawldraft.assets.AssetManager  mutableSetOf *com.example.brawldraft.assets.AssetManager  scanAssetsInPath *com.example.brawldraft.assets.AssetManager  scanAvailableAssets *com.example.brawldraft.assets.AssetManager  withContext *com.example.brawldraft.assets.AssetManager  BRAWLER_ICONS_PATH 4com.example.brawldraft.assets.AssetManager.Companion  Bitmap 4com.example.brawldraft.assets.AssetManager.Companion  
BitmapFactory 4com.example.brawldraft.assets.AssetManager.Companion  BrawlerDatabase 4com.example.brawldraft.assets.AssetManager.Companion  
CACHE_SIZE 4com.example.brawldraft.assets.AssetManager.Companion  Dispatchers 4com.example.brawldraft.assets.AssetManager.Companion  Log 4com.example.brawldraft.assets.AssetManager.Companion  LruCache 4com.example.brawldraft.assets.AssetManager.Companion  MAP_ICONS_PATH 4com.example.brawldraft.assets.AssetManager.Companion  TAG 4com.example.brawldraft.assets.AssetManager.Companion  TEMPLATE_ICONS_PATH 4com.example.brawldraft.assets.AssetManager.Companion  availableAssets 4com.example.brawldraft.assets.AssetManager.Companion  bitmapCache 4com.example.brawldraft.assets.AssetManager.Companion  com 4com.example.brawldraft.assets.AssetManager.Companion  generateBrawlerPlaceholder 4com.example.brawldraft.assets.AssetManager.Companion  generateMapPlaceholder 4com.example.brawldraft.assets.AssetManager.Companion  getAllBrawlers 4com.example.brawldraft.assets.AssetManager.Companion  getBrawlerIcon 4com.example.brawldraft.assets.AssetManager.Companion  let 4com.example.brawldraft.assets.AssetManager.Companion  loadBitmapFromAssets 4com.example.brawldraft.assets.AssetManager.Companion  mutableSetOf 4com.example.brawldraft.assets.AssetManager.Companion  scanAvailableAssets 4com.example.brawldraft.assets.AssetManager.Companion  withContext 4com.example.brawldraft.assets.AssetManager.Companion  Bitmap 2com.example.brawldraft.assets.PlaceholderGenerator  Brawler 2com.example.brawldraft.assets.PlaceholderGenerator  BrawlerDatabase 2com.example.brawldraft.assets.PlaceholderGenerator  Canvas 2com.example.brawldraft.assets.PlaceholderGenerator  Color 2com.example.brawldraft.assets.PlaceholderGenerator  Context 2com.example.brawldraft.assets.PlaceholderGenerator  Dispatchers 2com.example.brawldraft.assets.PlaceholderGenerator  	Exception 2com.example.brawldraft.assets.PlaceholderGenerator  File 2com.example.brawldraft.assets.PlaceholderGenerator  FileOutputStream 2com.example.brawldraft.assets.PlaceholderGenerator  	ICON_SIZE 2com.example.brawldraft.assets.PlaceholderGenerator  Paint 2com.example.brawldraft.assets.PlaceholderGenerator  	TEXT_SIZE 2com.example.brawldraft.assets.PlaceholderGenerator  Typeface 2com.example.brawldraft.assets.PlaceholderGenerator  apply 2com.example.brawldraft.assets.PlaceholderGenerator  com 2com.example.brawldraft.assets.PlaceholderGenerator  context 2com.example.brawldraft.assets.PlaceholderGenerator  generateBrawlerIcon 2com.example.brawldraft.assets.PlaceholderGenerator  getAllBrawlers 2com.example.brawldraft.assets.PlaceholderGenerator  withContext 2com.example.brawldraft.assets.PlaceholderGenerator  Bitmap <com.example.brawldraft.assets.PlaceholderGenerator.Companion  BrawlerDatabase <com.example.brawldraft.assets.PlaceholderGenerator.Companion  Canvas <com.example.brawldraft.assets.PlaceholderGenerator.Companion  Color <com.example.brawldraft.assets.PlaceholderGenerator.Companion  Dispatchers <com.example.brawldraft.assets.PlaceholderGenerator.Companion  File <com.example.brawldraft.assets.PlaceholderGenerator.Companion  FileOutputStream <com.example.brawldraft.assets.PlaceholderGenerator.Companion  	ICON_SIZE <com.example.brawldraft.assets.PlaceholderGenerator.Companion  Paint <com.example.brawldraft.assets.PlaceholderGenerator.Companion  	TEXT_SIZE <com.example.brawldraft.assets.PlaceholderGenerator.Companion  Typeface <com.example.brawldraft.assets.PlaceholderGenerator.Companion  apply <com.example.brawldraft.assets.PlaceholderGenerator.Companion  com <com.example.brawldraft.assets.PlaceholderGenerator.Companion  context <com.example.brawldraft.assets.PlaceholderGenerator.Companion  generateBrawlerIcon <com.example.brawldraft.assets.PlaceholderGenerator.Companion  getAllBrawlers <com.example.brawldraft.assets.PlaceholderGenerator.Companion  withContext <com.example.brawldraft.assets.PlaceholderGenerator.Companion  example com.example.brawldraft.com  
brawldraft "com.example.brawldraft.com.example  data -com.example.brawldraft.com.example.brawldraft  
DraftState 2com.example.brawldraft.com.example.brawldraft.data  	TeamState 2com.example.brawldraft.com.example.brawldraft.data  
ActionType com.example.brawldraft.data  AnalysisResult com.example.brawldraft.data  Bitmap com.example.brawldraft.data  Boolean com.example.brawldraft.data  Brawler com.example.brawldraft.data  BrawlerDatabase com.example.brawldraft.data  
BrawlerRarity com.example.brawldraft.data  BrawlerRecommendation com.example.brawldraft.data  BrawlerState com.example.brawldraft.data  BrawlerType com.example.brawldraft.data  Core com.example.brawldraft.data  DetectedBrawler com.example.brawldraft.data  Dispatchers com.example.brawldraft.data  DraftAction com.example.brawldraft.data  
DraftPhase com.example.brawldraft.data  
DraftState com.example.brawldraft.data  DraftStateUpdate com.example.brawldraft.data  	Exception com.example.brawldraft.data  ExpectedAction com.example.brawldraft.data  Float com.example.brawldraft.data  GameMap com.example.brawldraft.data  GameMode com.example.brawldraft.data  	GameState com.example.brawldraft.data  Imgproc com.example.brawldraft.data  Int com.example.brawldraft.data  List com.example.brawldraft.data  Log com.example.brawldraft.data  Long com.example.brawldraft.data  MATCH_THRESHOLD com.example.brawldraft.data  MAX_RECOMMENDATIONS com.example.brawldraft.data  MapDatabase com.example.brawldraft.data  Mat com.example.brawldraft.data  MutableStateFlow com.example.brawldraft.data  RecommendationEngine com.example.brawldraft.data  RecommendationPriority com.example.brawldraft.data  Rect com.example.brawldraft.data  Regex com.example.brawldraft.data  	StateFlow com.example.brawldraft.data  String com.example.brawldraft.data  System com.example.brawldraft.data  TAG com.example.brawldraft.data  TeamSide com.example.brawldraft.data  	TeamState com.example.brawldraft.data  TemplateMatcher com.example.brawldraft.data  Utils com.example.brawldraft.data  any com.example.brawldraft.data  asStateFlow com.example.brawldraft.data  average com.example.brawldraft.data  calculateOverallConfidence com.example.brawldraft.data  contains com.example.brawldraft.data  detectBrawlers com.example.brawldraft.data  detectDraftPhase com.example.brawldraft.data  detectGameState com.example.brawldraft.data  	detectMap com.example.brawldraft.data  empty com.example.brawldraft.data  	emptyList com.example.brawldraft.data  equals com.example.brawldraft.data  filter com.example.brawldraft.data  find com.example.brawldraft.data  getAllBrawlers com.example.brawldraft.data  
isNotEmpty com.example.brawldraft.data  joinToString com.example.brawldraft.data  listOf com.example.brawldraft.data  	lowercase com.example.brawldraft.data  map com.example.brawldraft.data  min com.example.brawldraft.data  
mutableListOf com.example.brawldraft.data  plus com.example.brawldraft.data  
plusAssign com.example.brawldraft.data  replace com.example.brawldraft.data  sortedByDescending com.example.brawldraft.data  take com.example.brawldraft.data  unknown com.example.brawldraft.data  until com.example.brawldraft.data  withContext com.example.brawldraft.data  BAN &com.example.brawldraft.data.ActionType  PICK &com.example.brawldraft.data.ActionType  name &com.example.brawldraft.data.ActionType  Brawler #com.example.brawldraft.data.Brawler  
BrawlerRarity #com.example.brawldraft.data.Brawler  BrawlerType #com.example.brawldraft.data.Brawler  Float #com.example.brawldraft.data.Brawler  String #com.example.brawldraft.data.Brawler  description #com.example.brawldraft.data.Brawler  id #com.example.brawldraft.data.Brawler  name #com.example.brawldraft.data.Brawler  pickRate #com.example.brawldraft.data.Brawler  rarity #com.example.brawldraft.data.Brawler  type #com.example.brawldraft.data.Brawler  winRate #com.example.brawldraft.data.Brawler  Brawler -com.example.brawldraft.data.Brawler.Companion  
BrawlerRarity -com.example.brawldraft.data.Brawler.Companion  BrawlerType -com.example.brawldraft.data.Brawler.Companion  Brawler +com.example.brawldraft.data.BrawlerDatabase  
BrawlerRarity +com.example.brawldraft.data.BrawlerDatabase  BrawlerType +com.example.brawldraft.data.BrawlerDatabase  brawlers +com.example.brawldraft.data.BrawlerDatabase  contains +com.example.brawldraft.data.BrawlerDatabase  equals +com.example.brawldraft.data.BrawlerDatabase  filter +com.example.brawldraft.data.BrawlerDatabase  find +com.example.brawldraft.data.BrawlerDatabase  getAllBrawlers +com.example.brawldraft.data.BrawlerDatabase  getBrawlerById +com.example.brawldraft.data.BrawlerDatabase  getBrawlerByName +com.example.brawldraft.data.BrawlerDatabase  listOf +com.example.brawldraft.data.BrawlerDatabase  sortedByDescending +com.example.brawldraft.data.BrawlerDatabase  take +com.example.brawldraft.data.BrawlerDatabase  	CHROMATIC )com.example.brawldraft.data.BrawlerRarity  COMMON )com.example.brawldraft.data.BrawlerRarity  EPIC )com.example.brawldraft.data.BrawlerRarity  	LEGENDARY )com.example.brawldraft.data.BrawlerRarity  MYTHIC )com.example.brawldraft.data.BrawlerRarity  RARE )com.example.brawldraft.data.BrawlerRarity  
SUPER_RARE )com.example.brawldraft.data.BrawlerRarity  	ARTILLERY 'com.example.brawldraft.data.BrawlerType  ASSASSIN 'com.example.brawldraft.data.BrawlerType  
CONTROLLER 'com.example.brawldraft.data.BrawlerType  
DAMAGE_DEALER 'com.example.brawldraft.data.BrawlerType  MARKSMAN 'com.example.brawldraft.data.BrawlerType  SUPPORT 'com.example.brawldraft.data.BrawlerType  TANK 'com.example.brawldraft.data.BrawlerType  	BAN_PHASE &com.example.brawldraft.data.DraftPhase  	COMPLETED &com.example.brawldraft.data.DraftPhase  PICK_PHASE_1 &com.example.brawldraft.data.DraftPhase  PICK_PHASE_2 &com.example.brawldraft.data.DraftPhase  PICK_PHASE_3 &com.example.brawldraft.data.DraftPhase  PICK_PHASE_4 &com.example.brawldraft.data.DraftPhase  WAITING &com.example.brawldraft.data.DraftPhase  name &com.example.brawldraft.data.DraftPhase  
ActionType &com.example.brawldraft.data.DraftState  
DraftPhase &com.example.brawldraft.data.DraftState  ExpectedAction &com.example.brawldraft.data.DraftState  TeamSide &com.example.brawldraft.data.DraftState  any &com.example.brawldraft.data.DraftState  bannedBrawlers &com.example.brawldraft.data.DraftState  blueTeam &com.example.brawldraft.data.DraftState  copy &com.example.brawldraft.data.DraftState  
currentMap &com.example.brawldraft.data.DraftState  currentPhase &com.example.brawldraft.data.DraftState  determineNextTeam &com.example.brawldraft.data.DraftState  draftHistory &com.example.brawldraft.data.DraftState  getEnemyTeam &com.example.brawldraft.data.DraftState  getNextExpectedAction &com.example.brawldraft.data.DraftState  
getPlayerTeam &com.example.brawldraft.data.DraftState  getTotalBans &com.example.brawldraft.data.DraftState  isBrawlerAvailable &com.example.brawldraft.data.DraftState  isBrawlerBanned &com.example.brawldraft.data.DraftState  isBrawlerPicked &com.example.brawldraft.data.DraftState  isPlayerBlueTeam &com.example.brawldraft.data.DraftState  redTeam &com.example.brawldraft.data.DraftState  Boolean ,com.example.brawldraft.data.DraftStateUpdate  Brawler ,com.example.brawldraft.data.DraftStateUpdate  
BrawlerBanned ,com.example.brawldraft.data.DraftStateUpdate  
BrawlerPicked ,com.example.brawldraft.data.DraftStateUpdate  DraftAction ,com.example.brawldraft.data.DraftStateUpdate  
DraftPhase ,com.example.brawldraft.data.DraftStateUpdate  
DraftReset ,com.example.brawldraft.data.DraftStateUpdate  DraftStateUpdate ,com.example.brawldraft.data.DraftStateUpdate  GameMap ,com.example.brawldraft.data.DraftStateUpdate  ManualCorrection ,com.example.brawldraft.data.DraftStateUpdate  MapDetected ,com.example.brawldraft.data.DraftStateUpdate  PhaseChanged ,com.example.brawldraft.data.DraftStateUpdate  TeamSide ,com.example.brawldraft.data.DraftStateUpdate  brawler :com.example.brawldraft.data.DraftStateUpdate.BrawlerBanned  team :com.example.brawldraft.data.DraftStateUpdate.BrawlerBanned  brawler :com.example.brawldraft.data.DraftStateUpdate.BrawlerPicked  team :com.example.brawldraft.data.DraftStateUpdate.BrawlerPicked  action =com.example.brawldraft.data.DraftStateUpdate.ManualCorrection  map 8com.example.brawldraft.data.DraftStateUpdate.MapDetected  newPhase 9com.example.brawldraft.data.DraftStateUpdate.PhaseChanged  
actionType *com.example.brawldraft.data.ExpectedAction  	Companion #com.example.brawldraft.data.GameMap  GameMap #com.example.brawldraft.data.GameMap  GameMode #com.example.brawldraft.data.GameMap  String #com.example.brawldraft.data.GameMap  description #com.example.brawldraft.data.GameMap  id #com.example.brawldraft.data.GameMap  let #com.example.brawldraft.data.GameMap  mode #com.example.brawldraft.data.GameMap  name #com.example.brawldraft.data.GameMap  unknown #com.example.brawldraft.data.GameMap  GameMap -com.example.brawldraft.data.GameMap.Companion  GameMode -com.example.brawldraft.data.GameMap.Companion  unknown -com.example.brawldraft.data.GameMap.Companion  BOUNTY $com.example.brawldraft.data.GameMode  
BRAWL_BALL $com.example.brawldraft.data.GameMode  GEM_GRAB $com.example.brawldraft.data.GameMode  HEIST $com.example.brawldraft.data.GameMode  HOT_ZONE $com.example.brawldraft.data.GameMode  KNOCKOUT $com.example.brawldraft.data.GameMode  SHOWDOWN $com.example.brawldraft.data.GameMode  SIEGE $com.example.brawldraft.data.GameMode  UNKNOWN $com.example.brawldraft.data.GameMode  GameMap 'com.example.brawldraft.data.MapDatabase  GameMode 'com.example.brawldraft.data.MapDatabase  Regex 'com.example.brawldraft.data.MapDatabase  contains 'com.example.brawldraft.data.MapDatabase  equals 'com.example.brawldraft.data.MapDatabase  filter 'com.example.brawldraft.data.MapDatabase  find 'com.example.brawldraft.data.MapDatabase  getMapByName 'com.example.brawldraft.data.MapDatabase  listOf 'com.example.brawldraft.data.MapDatabase  	lowercase 'com.example.brawldraft.data.MapDatabase  maps 'com.example.brawldraft.data.MapDatabase  replace 'com.example.brawldraft.data.MapDatabase  BLUE $com.example.brawldraft.data.TeamSide  RED $com.example.brawldraft.data.TeamSide  any %com.example.brawldraft.data.TeamState  bannedBrawlers %com.example.brawldraft.data.TeamState  copy %com.example.brawldraft.data.TeamState  pickedBrawlers %com.example.brawldraft.data.TeamState  Boolean com.example.brawldraft.ui  BrawlerRecommendation com.example.brawldraft.ui  
ContextCompat com.example.brawldraft.ui  DiffUtil com.example.brawldraft.ui  Int com.example.brawldraft.ui  LayoutInflater com.example.brawldraft.ui  ListAdapter com.example.brawldraft.ui  R com.example.brawldraft.ui  RecommendationAdapter com.example.brawldraft.ui  RecommendationPriority com.example.brawldraft.ui  RecommendationViewHolder com.example.brawldraft.ui  RecyclerView com.example.brawldraft.ui  TextView com.example.brawldraft.ui  Unit com.example.brawldraft.ui  View com.example.brawldraft.ui  	ViewGroup com.example.brawldraft.ui  onRecommendationClick com.example.brawldraft.ui  ItemCallback "com.example.brawldraft.ui.DiffUtil  Boolean /com.example.brawldraft.ui.RecommendationAdapter  BrawlerRecommendation /com.example.brawldraft.ui.RecommendationAdapter  
ContextCompat /com.example.brawldraft.ui.RecommendationAdapter  DiffCallback /com.example.brawldraft.ui.RecommendationAdapter  DiffUtil /com.example.brawldraft.ui.RecommendationAdapter  Int /com.example.brawldraft.ui.RecommendationAdapter  LayoutInflater /com.example.brawldraft.ui.RecommendationAdapter  R /com.example.brawldraft.ui.RecommendationAdapter  RecommendationPriority /com.example.brawldraft.ui.RecommendationAdapter  RecommendationViewHolder /com.example.brawldraft.ui.RecommendationAdapter  RecyclerView /com.example.brawldraft.ui.RecommendationAdapter  TextView /com.example.brawldraft.ui.RecommendationAdapter  Unit /com.example.brawldraft.ui.RecommendationAdapter  View /com.example.brawldraft.ui.RecommendationAdapter  	ViewGroup /com.example.brawldraft.ui.RecommendationAdapter  getItem /com.example.brawldraft.ui.RecommendationAdapter  onRecommendationClick /com.example.brawldraft.ui.RecommendationAdapter  
submitList /com.example.brawldraft.ui.RecommendationAdapter  ItemCallback 8com.example.brawldraft.ui.RecommendationAdapter.DiffUtil  
ContextCompat Hcom.example.brawldraft.ui.RecommendationAdapter.RecommendationViewHolder  R Hcom.example.brawldraft.ui.RecommendationAdapter.RecommendationViewHolder  RecommendationPriority Hcom.example.brawldraft.ui.RecommendationAdapter.RecommendationViewHolder  bind Hcom.example.brawldraft.ui.RecommendationAdapter.RecommendationViewHolder  brawlerName Hcom.example.brawldraft.ui.RecommendationAdapter.RecommendationViewHolder  itemView Hcom.example.brawldraft.ui.RecommendationAdapter.RecommendationViewHolder  onRecommendationClick Hcom.example.brawldraft.ui.RecommendationAdapter.RecommendationViewHolder  priorityIndicator Hcom.example.brawldraft.ui.RecommendationAdapter.RecommendationViewHolder  
reasonText Hcom.example.brawldraft.ui.RecommendationAdapter.RecommendationViewHolder  	scoreText Hcom.example.brawldraft.ui.RecommendationAdapter.RecommendationViewHolder  
ViewHolder <com.example.brawldraft.ui.RecommendationAdapter.RecyclerView  
ViewHolder &com.example.brawldraft.ui.RecyclerView  AnalysisResult com.example.brawldraft.vision  AssetManager com.example.brawldraft.vision  Bitmap com.example.brawldraft.vision  
BitmapFactory com.example.brawldraft.vision  Boolean com.example.brawldraft.vision  Brawler com.example.brawldraft.vision  BrawlerDatabase com.example.brawldraft.vision  BrawlerState com.example.brawldraft.vision  Context com.example.brawldraft.vision  Core com.example.brawldraft.vision  CvType com.example.brawldraft.vision  DetectedBrawler com.example.brawldraft.vision  Dispatchers com.example.brawldraft.vision  Double com.example.brawldraft.vision  
DraftPhase com.example.brawldraft.vision  	Exception com.example.brawldraft.vision  Float com.example.brawldraft.vision  GameMap com.example.brawldraft.vision  	GameState com.example.brawldraft.vision  IOException com.example.brawldraft.vision  
ImageAnalyzer com.example.brawldraft.vision  Imgproc com.example.brawldraft.vision  Int com.example.brawldraft.vision  List com.example.brawldraft.vision  Log com.example.brawldraft.vision  Long com.example.brawldraft.vision  MATCH_THRESHOLD com.example.brawldraft.vision  Mat com.example.brawldraft.vision  Rect com.example.brawldraft.vision  Scalar com.example.brawldraft.vision  Size com.example.brawldraft.vision  String com.example.brawldraft.vision  System com.example.brawldraft.vision  TAG com.example.brawldraft.vision  TEMPLATE_MATCH_THRESHOLD com.example.brawldraft.vision  TemplateMatchResult com.example.brawldraft.vision  TemplateMatcher com.example.brawldraft.vision  Utils com.example.brawldraft.vision  average com.example.brawldraft.vision  calculateOverallConfidence com.example.brawldraft.vision  
component1 com.example.brawldraft.vision  
component2 com.example.brawldraft.vision  detectBrawlers com.example.brawldraft.vision  detectDraftPhase com.example.brawldraft.vision  detectGameState com.example.brawldraft.vision  	detectMap com.example.brawldraft.vision  empty com.example.brawldraft.vision  	emptyList com.example.brawldraft.vision  getAllBrawlers com.example.brawldraft.vision  getBrawlerById com.example.brawldraft.vision  
isNotEmpty com.example.brawldraft.vision  iterator com.example.brawldraft.vision  map com.example.brawldraft.vision  
mutableListOf com.example.brawldraft.vision  mutableMapOf com.example.brawldraft.vision  
plusAssign com.example.brawldraft.vision  set com.example.brawldraft.vision  sortedByDescending com.example.brawldraft.vision  take com.example.brawldraft.vision  until com.example.brawldraft.vision  withContext com.example.brawldraft.vision  AnalysisResult ,com.example.brawldraft.vision.AnalysisResult  	Companion ,com.example.brawldraft.vision.AnalysisResult  DetectedBrawler ,com.example.brawldraft.vision.AnalysisResult  
DraftPhase ,com.example.brawldraft.vision.AnalysisResult  Float ,com.example.brawldraft.vision.AnalysisResult  GameMap ,com.example.brawldraft.vision.AnalysisResult  	GameState ,com.example.brawldraft.vision.AnalysisResult  List ,com.example.brawldraft.vision.AnalysisResult  Long ,com.example.brawldraft.vision.AnalysisResult  System ,com.example.brawldraft.vision.AnalysisResult  
confidence ,com.example.brawldraft.vision.AnalysisResult  currentPhase ,com.example.brawldraft.vision.AnalysisResult  detectedBrawlers ,com.example.brawldraft.vision.AnalysisResult  detectedMap ,com.example.brawldraft.vision.AnalysisResult  empty ,com.example.brawldraft.vision.AnalysisResult  	emptyList ,com.example.brawldraft.vision.AnalysisResult  	gameState ,com.example.brawldraft.vision.AnalysisResult  AnalysisResult 6com.example.brawldraft.vision.AnalysisResult.Companion  
DraftPhase 6com.example.brawldraft.vision.AnalysisResult.Companion  	GameState 6com.example.brawldraft.vision.AnalysisResult.Companion  System 6com.example.brawldraft.vision.AnalysisResult.Companion  empty 6com.example.brawldraft.vision.AnalysisResult.Companion  	emptyList 6com.example.brawldraft.vision.AnalysisResult.Companion  UNKNOWN *com.example.brawldraft.vision.BrawlerState  
confidence -com.example.brawldraft.vision.DetectedBrawler  DRAFT_BAN_PHASE 'com.example.brawldraft.vision.GameState  DRAFT_PICK_PHASE 'com.example.brawldraft.vision.GameState  MENU 'com.example.brawldraft.vision.GameState  UNKNOWN 'com.example.brawldraft.vision.GameState  name 'com.example.brawldraft.vision.GameState  AnalysisResult +com.example.brawldraft.vision.ImageAnalyzer  Bitmap +com.example.brawldraft.vision.ImageAnalyzer  Boolean +com.example.brawldraft.vision.ImageAnalyzer  BrawlerState +com.example.brawldraft.vision.ImageAnalyzer  Core +com.example.brawldraft.vision.ImageAnalyzer  DetectedBrawler +com.example.brawldraft.vision.ImageAnalyzer  Dispatchers +com.example.brawldraft.vision.ImageAnalyzer  
DraftPhase +com.example.brawldraft.vision.ImageAnalyzer  	Exception +com.example.brawldraft.vision.ImageAnalyzer  Float +com.example.brawldraft.vision.ImageAnalyzer  GameMap +com.example.brawldraft.vision.ImageAnalyzer  	GameState +com.example.brawldraft.vision.ImageAnalyzer  Imgproc +com.example.brawldraft.vision.ImageAnalyzer  List +com.example.brawldraft.vision.ImageAnalyzer  Log +com.example.brawldraft.vision.ImageAnalyzer  MATCH_THRESHOLD +com.example.brawldraft.vision.ImageAnalyzer  Mat +com.example.brawldraft.vision.ImageAnalyzer  Rect +com.example.brawldraft.vision.ImageAnalyzer  System +com.example.brawldraft.vision.ImageAnalyzer  TAG +com.example.brawldraft.vision.ImageAnalyzer  TemplateMatcher +com.example.brawldraft.vision.ImageAnalyzer  Utils +com.example.brawldraft.vision.ImageAnalyzer  analyzeScreenCapture +com.example.brawldraft.vision.ImageAnalyzer  average +com.example.brawldraft.vision.ImageAnalyzer  calculateOverallConfidence +com.example.brawldraft.vision.ImageAnalyzer  checkForBanPhaseUI +com.example.brawldraft.vision.ImageAnalyzer  checkForDraftUI +com.example.brawldraft.vision.ImageAnalyzer  checkForPickPhaseUI +com.example.brawldraft.vision.ImageAnalyzer  detectBrawlerInRegion +com.example.brawldraft.vision.ImageAnalyzer  detectBrawlers +com.example.brawldraft.vision.ImageAnalyzer  detectDraftPhase +com.example.brawldraft.vision.ImageAnalyzer  detectGameState +com.example.brawldraft.vision.ImageAnalyzer  	detectMap +com.example.brawldraft.vision.ImageAnalyzer  empty +com.example.brawldraft.vision.ImageAnalyzer  getBrawlerIconRegions +com.example.brawldraft.vision.ImageAnalyzer  
isNotEmpty +com.example.brawldraft.vision.ImageAnalyzer  map +com.example.brawldraft.vision.ImageAnalyzer  
mutableListOf +com.example.brawldraft.vision.ImageAnalyzer  
plusAssign +com.example.brawldraft.vision.ImageAnalyzer  templateMatcher +com.example.brawldraft.vision.ImageAnalyzer  until +com.example.brawldraft.vision.ImageAnalyzer  withContext +com.example.brawldraft.vision.ImageAnalyzer  AnalysisResult 5com.example.brawldraft.vision.ImageAnalyzer.Companion  BrawlerState 5com.example.brawldraft.vision.ImageAnalyzer.Companion  Core 5com.example.brawldraft.vision.ImageAnalyzer.Companion  DetectedBrawler 5com.example.brawldraft.vision.ImageAnalyzer.Companion  Dispatchers 5com.example.brawldraft.vision.ImageAnalyzer.Companion  
DraftPhase 5com.example.brawldraft.vision.ImageAnalyzer.Companion  	GameState 5com.example.brawldraft.vision.ImageAnalyzer.Companion  Imgproc 5com.example.brawldraft.vision.ImageAnalyzer.Companion  Log 5com.example.brawldraft.vision.ImageAnalyzer.Companion  MATCH_THRESHOLD 5com.example.brawldraft.vision.ImageAnalyzer.Companion  Mat 5com.example.brawldraft.vision.ImageAnalyzer.Companion  Rect 5com.example.brawldraft.vision.ImageAnalyzer.Companion  System 5com.example.brawldraft.vision.ImageAnalyzer.Companion  TAG 5com.example.brawldraft.vision.ImageAnalyzer.Companion  TemplateMatcher 5com.example.brawldraft.vision.ImageAnalyzer.Companion  Utils 5com.example.brawldraft.vision.ImageAnalyzer.Companion  average 5com.example.brawldraft.vision.ImageAnalyzer.Companion  calculateOverallConfidence 5com.example.brawldraft.vision.ImageAnalyzer.Companion  detectBrawlers 5com.example.brawldraft.vision.ImageAnalyzer.Companion  detectDraftPhase 5com.example.brawldraft.vision.ImageAnalyzer.Companion  detectGameState 5com.example.brawldraft.vision.ImageAnalyzer.Companion  	detectMap 5com.example.brawldraft.vision.ImageAnalyzer.Companion  empty 5com.example.brawldraft.vision.ImageAnalyzer.Companion  
isNotEmpty 5com.example.brawldraft.vision.ImageAnalyzer.Companion  map 5com.example.brawldraft.vision.ImageAnalyzer.Companion  
mutableListOf 5com.example.brawldraft.vision.ImageAnalyzer.Companion  
plusAssign 5com.example.brawldraft.vision.ImageAnalyzer.Companion  until 5com.example.brawldraft.vision.ImageAnalyzer.Companion  withContext 5com.example.brawldraft.vision.ImageAnalyzer.Companion  brawler 1com.example.brawldraft.vision.TemplateMatchResult  
confidence 1com.example.brawldraft.vision.TemplateMatchResult  AssetManager -com.example.brawldraft.vision.TemplateMatcher  
BitmapFactory -com.example.brawldraft.vision.TemplateMatcher  BrawlerDatabase -com.example.brawldraft.vision.TemplateMatcher  Context -com.example.brawldraft.vision.TemplateMatcher  Core -com.example.brawldraft.vision.TemplateMatcher  CvType -com.example.brawldraft.vision.TemplateMatcher  Double -com.example.brawldraft.vision.TemplateMatcher  	Exception -com.example.brawldraft.vision.TemplateMatcher  IOException -com.example.brawldraft.vision.TemplateMatcher  Imgproc -com.example.brawldraft.vision.TemplateMatcher  Int -com.example.brawldraft.vision.TemplateMatcher  List -com.example.brawldraft.vision.TemplateMatcher  Log -com.example.brawldraft.vision.TemplateMatcher  Mat -com.example.brawldraft.vision.TemplateMatcher  Scalar -com.example.brawldraft.vision.TemplateMatcher  Size -com.example.brawldraft.vision.TemplateMatcher  String -com.example.brawldraft.vision.TemplateMatcher  TAG -com.example.brawldraft.vision.TemplateMatcher  TEMPLATE_MATCH_THRESHOLD -com.example.brawldraft.vision.TemplateMatcher  TemplateMatchResult -com.example.brawldraft.vision.TemplateMatcher  Utils -com.example.brawldraft.vision.TemplateMatcher  brawlerTemplates -com.example.brawldraft.vision.TemplateMatcher  cleanup -com.example.brawldraft.vision.TemplateMatcher  
component1 -com.example.brawldraft.vision.TemplateMatcher  
component2 -com.example.brawldraft.vision.TemplateMatcher  createPlaceholderTemplate -com.example.brawldraft.vision.TemplateMatcher  	emptyList -com.example.brawldraft.vision.TemplateMatcher  getAllBrawlers -com.example.brawldraft.vision.TemplateMatcher  getBrawlerById -com.example.brawldraft.vision.TemplateMatcher  
initialize -com.example.brawldraft.vision.TemplateMatcher  
isInitialized -com.example.brawldraft.vision.TemplateMatcher  iterator -com.example.brawldraft.vision.TemplateMatcher  loadBrawlerTemplates -com.example.brawldraft.vision.TemplateMatcher  loadTemplateFromAssets -com.example.brawldraft.vision.TemplateMatcher  matchBrawlerTemplate -com.example.brawldraft.vision.TemplateMatcher  
mutableListOf -com.example.brawldraft.vision.TemplateMatcher  mutableMapOf -com.example.brawldraft.vision.TemplateMatcher  performActualTemplateMatch -com.example.brawldraft.vision.TemplateMatcher  performTemplateMatch -com.example.brawldraft.vision.TemplateMatcher  set -com.example.brawldraft.vision.TemplateMatcher  sortedByDescending -com.example.brawldraft.vision.TemplateMatcher  take -com.example.brawldraft.vision.TemplateMatcher  
BitmapFactory 7com.example.brawldraft.vision.TemplateMatcher.Companion  BrawlerDatabase 7com.example.brawldraft.vision.TemplateMatcher.Companion  Core 7com.example.brawldraft.vision.TemplateMatcher.Companion  CvType 7com.example.brawldraft.vision.TemplateMatcher.Companion  Imgproc 7com.example.brawldraft.vision.TemplateMatcher.Companion  Log 7com.example.brawldraft.vision.TemplateMatcher.Companion  Mat 7com.example.brawldraft.vision.TemplateMatcher.Companion  Scalar 7com.example.brawldraft.vision.TemplateMatcher.Companion  Size 7com.example.brawldraft.vision.TemplateMatcher.Companion  TAG 7com.example.brawldraft.vision.TemplateMatcher.Companion  TEMPLATE_MATCH_THRESHOLD 7com.example.brawldraft.vision.TemplateMatcher.Companion  TemplateMatchResult 7com.example.brawldraft.vision.TemplateMatcher.Companion  Utils 7com.example.brawldraft.vision.TemplateMatcher.Companion  
component1 7com.example.brawldraft.vision.TemplateMatcher.Companion  
component2 7com.example.brawldraft.vision.TemplateMatcher.Companion  	emptyList 7com.example.brawldraft.vision.TemplateMatcher.Companion  getAllBrawlers 7com.example.brawldraft.vision.TemplateMatcher.Companion  getBrawlerById 7com.example.brawldraft.vision.TemplateMatcher.Companion  iterator 7com.example.brawldraft.vision.TemplateMatcher.Companion  
mutableListOf 7com.example.brawldraft.vision.TemplateMatcher.Companion  mutableMapOf 7com.example.brawldraft.vision.TemplateMatcher.Companion  set 7com.example.brawldraft.vision.TemplateMatcher.Companion  sortedByDescending 7com.example.brawldraft.vision.TemplateMatcher.Companion  take 7com.example.brawldraft.vision.TemplateMatcher.Companion  File java.io  FileOutputStream java.io  IOException java.io  InputStream java.io  exists java.io.File  mkdirs java.io.File  close java.io.FileOutputStream  close java.io.InputStream  Class 	java.lang  	Exception 	java.lang  Runnable 	java.lang  
StringBuilder 	java.lang  printStackTrace java.lang.Exception  <SAM-CONSTRUCTOR> java.lang.Runnable  append java.lang.StringBuilder  currentTimeMillis java.lang.System  
BigDecimal 	java.math  
BigInteger 	java.math  
ByteBuffer java.nio  Array kotlin  BooleanArray kotlin  	ByteArray kotlin  	CharArray kotlin  CharSequence kotlin  DoubleArray kotlin  
FloatArray kotlin  	Function0 kotlin  	Function1 kotlin  IntArray kotlin  	LongArray kotlin  Nothing kotlin  Result kotlin  
ShortArray kotlin  Suppress kotlin  
UByteArray kotlin  	UIntArray kotlin  
ULongArray kotlin  UShortArray kotlin  apply kotlin  
isInitialized kotlin  let kotlin  map kotlin  plus kotlin  equals 
kotlin.Any  get kotlin.Array  iterator kotlin.Array  not kotlin.Boolean  	compareTo 
kotlin.Double  plus 
kotlin.Double  toFloat 
kotlin.Double  toInt 
kotlin.Double  get kotlin.DoubleArray  	compareTo kotlin.Float  div kotlin.Float  minus kotlin.Float  plus kotlin.Float  
plusAssign kotlin.Float  times kotlin.Float  toInt kotlin.Float  invoke kotlin.Function1  	compareTo 
kotlin.Int  div 
kotlin.Int  inc 
kotlin.Int  minus 
kotlin.Int  or 
kotlin.Int  plus 
kotlin.Int  rem 
kotlin.Int  times 
kotlin.Int  toFloat 
kotlin.Int  toInt kotlin.Long  contains 
kotlin.String  equals 
kotlin.String  	lowercase 
kotlin.String  plus 
kotlin.String  replace 
kotlin.String  printStackTrace kotlin.Throwable  ByteIterator kotlin.collections  CharIterator kotlin.collections  IntIterator kotlin.collections  Iterator kotlin.collections  List kotlin.collections  Map kotlin.collections  MutableCollection kotlin.collections  MutableIterator kotlin.collections  MutableList kotlin.collections  
MutableMap kotlin.collections  
MutableSet kotlin.collections  Set kotlin.collections  any kotlin.collections  average kotlin.collections  
component1 kotlin.collections  
component2 kotlin.collections  contains kotlin.collections  	emptyList kotlin.collections  filter kotlin.collections  find kotlin.collections  
isNotEmpty kotlin.collections  iterator kotlin.collections  joinToString kotlin.collections  listOf kotlin.collections  map kotlin.collections  min kotlin.collections  mutableIterator kotlin.collections  
mutableListOf kotlin.collections  mutableMapOf kotlin.collections  mutableSetOf kotlin.collections  plus kotlin.collections  
plusAssign kotlin.collections  set kotlin.collections  sortedByDescending kotlin.collections  take kotlin.collections  hasNext kotlin.collections.IntIterator  next kotlin.collections.IntIterator  hasNext kotlin.collections.Iterator  next kotlin.collections.Iterator  any kotlin.collections.List  average kotlin.collections.List  contains kotlin.collections.List  filter kotlin.collections.List  find kotlin.collections.List  isEmpty kotlin.collections.List  
isNotEmpty kotlin.collections.List  iterator kotlin.collections.List  map kotlin.collections.List  plus kotlin.collections.List  size kotlin.collections.List  sortedByDescending kotlin.collections.List  take kotlin.collections.List  Entry kotlin.collections.Map  iterator $kotlin.collections.MutableCollection  hasNext "kotlin.collections.MutableIterator  next "kotlin.collections.MutableIterator  add kotlin.collections.MutableList  
isNotEmpty kotlin.collections.MutableList  joinToString kotlin.collections.MutableList  sortedByDescending kotlin.collections.MutableList  MutableEntry kotlin.collections.MutableMap  clear kotlin.collections.MutableMap  iterator kotlin.collections.MutableMap  set kotlin.collections.MutableMap  size kotlin.collections.MutableMap  values kotlin.collections.MutableMap  
component1 *kotlin.collections.MutableMap.MutableEntry  
component2 *kotlin.collections.MutableMap.MutableEntry  add kotlin.collections.MutableSet  contains kotlin.collections.MutableSet  size kotlin.collections.MutableSet  CoroutineContext kotlin.coroutines  SuspendFunction1 kotlin.coroutines  plus "kotlin.coroutines.CoroutineContext  iterator 	kotlin.io  java 
kotlin.jvm  max kotlin.math  min kotlin.math  	CharRange 
kotlin.ranges  IntRange 
kotlin.ranges  	LongRange 
kotlin.ranges  	UIntRange 
kotlin.ranges  
ULongRange 
kotlin.ranges  contains 
kotlin.ranges  until 
kotlin.ranges  iterator kotlin.ranges.IntProgression  iterator kotlin.ranges.IntRange  KMutableProperty0 kotlin.reflect  java kotlin.reflect.KClass  
isInitialized  kotlin.reflect.KMutableProperty0  Sequence kotlin.sequences  any kotlin.sequences  average kotlin.sequences  contains kotlin.sequences  filter kotlin.sequences  find kotlin.sequences  iterator kotlin.sequences  joinToString kotlin.sequences  map kotlin.sequences  min kotlin.sequences  plus kotlin.sequences  sortedByDescending kotlin.sequences  take kotlin.sequences  Regex kotlin.text  any kotlin.text  buildString kotlin.text  contains kotlin.text  equals kotlin.text  filter kotlin.text  find kotlin.text  
isNotEmpty kotlin.text  iterator kotlin.text  	lowercase kotlin.text  map kotlin.text  min kotlin.text  plus kotlin.text  replace kotlin.text  set kotlin.text  take kotlin.text  ACTION_START_CAPTURE kotlinx.coroutines  ACTION_STOP_CAPTURE kotlinx.coroutines  Activity kotlinx.coroutines  Bitmap kotlinx.coroutines  Boolean kotlinx.coroutines  BrawlerRecommendation kotlinx.coroutines  BroadcastReceiver kotlinx.coroutines  Build kotlinx.coroutines  Button kotlinx.coroutines  CAPTURE_INTERVAL_MS kotlinx.coroutines  
CHANNEL_ID kotlinx.coroutines  CompletableJob kotlinx.coroutines  Context kotlinx.coroutines  CoroutineDispatcher kotlinx.coroutines  CoroutineScope kotlinx.coroutines  Delay kotlinx.coroutines  Dispatchers kotlinx.coroutines  DisplayManager kotlinx.coroutines  DisplayMetrics kotlinx.coroutines  
DraftPhase kotlinx.coroutines  DraftStateManager kotlinx.coroutines  EXTRA_RESULT_CODE kotlinx.coroutines  EXTRA_RESULT_DATA kotlinx.coroutines  	Exception kotlinx.coroutines  Float kotlinx.coroutines  FrameLayout kotlinx.coroutines  Gravity kotlinx.coroutines  Handler kotlinx.coroutines  IBinder kotlinx.coroutines  Image kotlinx.coroutines  
ImageAnalyzer kotlinx.coroutines  ImageReader kotlinx.coroutines  Int kotlinx.coroutines  Intent kotlinx.coroutines  IntentFilter kotlinx.coroutines  Job kotlinx.coroutines  LayoutInflater kotlinx.coroutines  LinearLayout kotlinx.coroutines  LinearLayoutManager kotlinx.coroutines  List kotlinx.coroutines  Log kotlinx.coroutines  Looper kotlinx.coroutines  MEDIA_PROJECTION_SERVICE kotlinx.coroutines  MainCoroutineDispatcher kotlinx.coroutines  MediaProjection kotlinx.coroutines  MediaProjectionManager kotlinx.coroutines  MotionEvent kotlinx.coroutines  NOTIFICATION_ID kotlinx.coroutines  Notification kotlinx.coroutines  NotificationChannel kotlinx.coroutines  NotificationCompat kotlinx.coroutines  NotificationManager kotlinx.coroutines  PixelFormat kotlinx.coroutines  R kotlinx.coroutines  RecommendationAdapter kotlinx.coroutines  RecyclerView kotlinx.coroutines  START_NOT_STICKY kotlinx.coroutines  ScreenCaptureManager kotlinx.coroutines  ScreenCapturePermissionActivity kotlinx.coroutines  Service kotlinx.coroutines  String kotlinx.coroutines  
SupervisorJob kotlinx.coroutines  Suppress kotlinx.coroutines  SuppressLint kotlinx.coroutines  TAG kotlinx.coroutines  TemplateMatcher kotlinx.coroutines  TextView kotlinx.coroutines  Toast kotlinx.coroutines  View kotlinx.coroutines  VirtualDisplay kotlinx.coroutines  WINDOW_SERVICE kotlinx.coroutines  
WindowManager kotlinx.coroutines  android kotlinx.coroutines  apply kotlinx.coroutines  buildString kotlinx.coroutines  cancel kotlinx.coroutines  com kotlinx.coroutines  delay kotlinx.coroutines  draftStateManager kotlinx.coroutines  floatingView kotlinx.coroutines  getBrawlerByName kotlinx.coroutines  handleAnalysisResult kotlinx.coroutines  
imageAnalyzer kotlinx.coroutines  isActive kotlinx.coroutines  
isInitialized kotlinx.coroutines  
isNotEmpty kotlinx.coroutines  java kotlinx.coroutines  launch kotlinx.coroutines  let kotlinx.coroutines  params kotlinx.coroutines  processCapturedBitmap kotlinx.coroutines  runBlocking kotlinx.coroutines  
sendBroadcast kotlinx.coroutines  startScreenCapture kotlinx.coroutines  updateDraftStatusUI kotlinx.coroutines  updateDraftTeamsUI kotlinx.coroutines  updateRecommendationsUI kotlinx.coroutines  
windowManager kotlinx.coroutines  withContext kotlinx.coroutines  plus &kotlinx.coroutines.CoroutineDispatcher  AnalysisResult !kotlinx.coroutines.CoroutineScope  BRAWLER_ICONS_PATH !kotlinx.coroutines.CoroutineScope  BrawlerDatabase !kotlinx.coroutines.CoroutineScope  CAPTURE_INTERVAL_MS !kotlinx.coroutines.CoroutineScope  File !kotlinx.coroutines.CoroutineScope  Imgproc !kotlinx.coroutines.CoroutineScope  Log !kotlinx.coroutines.CoroutineScope  MAP_ICONS_PATH !kotlinx.coroutines.CoroutineScope  Mat !kotlinx.coroutines.CoroutineScope  System !kotlinx.coroutines.CoroutineScope  TAG !kotlinx.coroutines.CoroutineScope  TEMPLATE_ICONS_PATH !kotlinx.coroutines.CoroutineScope  Utils !kotlinx.coroutines.CoroutineScope  android !kotlinx.coroutines.CoroutineScope  apply !kotlinx.coroutines.CoroutineScope  availableAssets !kotlinx.coroutines.CoroutineScope  bitmapCache !kotlinx.coroutines.CoroutineScope  calculateOverallConfidence !kotlinx.coroutines.CoroutineScope  cancel !kotlinx.coroutines.CoroutineScope  context !kotlinx.coroutines.CoroutineScope  delay !kotlinx.coroutines.CoroutineScope  detectBrawlers !kotlinx.coroutines.CoroutineScope  detectDraftPhase !kotlinx.coroutines.CoroutineScope  detectGameState !kotlinx.coroutines.CoroutineScope  	detectMap !kotlinx.coroutines.CoroutineScope  draftStateManager !kotlinx.coroutines.CoroutineScope  empty !kotlinx.coroutines.CoroutineScope  generateBrawlerIcon !kotlinx.coroutines.CoroutineScope  generateBrawlerPlaceholder !kotlinx.coroutines.CoroutineScope  generateMapPlaceholder !kotlinx.coroutines.CoroutineScope  getAllBrawlers !kotlinx.coroutines.CoroutineScope  getBrawlerIcon !kotlinx.coroutines.CoroutineScope  
imageAnalyzer !kotlinx.coroutines.CoroutineScope  isActive !kotlinx.coroutines.CoroutineScope  launch !kotlinx.coroutines.CoroutineScope  let !kotlinx.coroutines.CoroutineScope  loadBitmapFromAssets !kotlinx.coroutines.CoroutineScope  processCapturedBitmap !kotlinx.coroutines.CoroutineScope  scanAvailableAssets !kotlinx.coroutines.CoroutineScope  
sendBroadcast !kotlinx.coroutines.CoroutineScope  updateDraftStatusUI !kotlinx.coroutines.CoroutineScope  updateDraftTeamsUI !kotlinx.coroutines.CoroutineScope  updateRecommendationsUI !kotlinx.coroutines.CoroutineScope  Default kotlinx.coroutines.Dispatchers  IO kotlinx.coroutines.Dispatchers  Main kotlinx.coroutines.Dispatchers  cancel kotlinx.coroutines.Job  plus *kotlinx.coroutines.MainCoroutineDispatcher  OnTouchListener kotlinx.coroutines.View  LayoutParams  kotlinx.coroutines.WindowManager  example kotlinx.coroutines.com  
brawldraft kotlinx.coroutines.com.example  data )kotlinx.coroutines.com.example.brawldraft  
DraftState .kotlinx.coroutines.com.example.brawldraft.data  	TeamState .kotlinx.coroutines.com.example.brawldraft.data  
FlowCollector kotlinx.coroutines.flow  MutableStateFlow kotlinx.coroutines.flow  	StateFlow kotlinx.coroutines.flow  asStateFlow kotlinx.coroutines.flow  collect kotlinx.coroutines.flow  <SAM-CONSTRUCTOR> %kotlinx.coroutines.flow.FlowCollector  asStateFlow (kotlinx.coroutines.flow.MutableStateFlow  value (kotlinx.coroutines.flow.MutableStateFlow  collect !kotlinx.coroutines.flow.StateFlow  OpenCVLoader org.opencv.android  Utils org.opencv.android  	initDebug org.opencv.android.OpenCVLoader  bitmapToMat org.opencv.android.Utils  AnalysisResult org.opencv.core  AssetManager org.opencv.core  Bitmap org.opencv.core  
BitmapFactory org.opencv.core  Boolean org.opencv.core  Brawler org.opencv.core  BrawlerDatabase org.opencv.core  BrawlerState org.opencv.core  Context org.opencv.core  Core org.opencv.core  CvType org.opencv.core  DetectedBrawler org.opencv.core  Dispatchers org.opencv.core  Double org.opencv.core  
DraftPhase org.opencv.core  	Exception org.opencv.core  Float org.opencv.core  GameMap org.opencv.core  	GameState org.opencv.core  IOException org.opencv.core  Imgproc org.opencv.core  Int org.opencv.core  List org.opencv.core  Log org.opencv.core  Long org.opencv.core  MATCH_THRESHOLD org.opencv.core  Mat org.opencv.core  Rect org.opencv.core  Scalar org.opencv.core  Size org.opencv.core  String org.opencv.core  System org.opencv.core  TAG org.opencv.core  TEMPLATE_MATCH_THRESHOLD org.opencv.core  TemplateMatchResult org.opencv.core  TemplateMatcher org.opencv.core  Utils org.opencv.core  average org.opencv.core  calculateOverallConfidence org.opencv.core  
component1 org.opencv.core  
component2 org.opencv.core  detectBrawlers org.opencv.core  detectDraftPhase org.opencv.core  detectGameState org.opencv.core  	detectMap org.opencv.core  empty org.opencv.core  	emptyList org.opencv.core  getAllBrawlers org.opencv.core  getBrawlerById org.opencv.core  
isNotEmpty org.opencv.core  iterator org.opencv.core  map org.opencv.core  
mutableListOf org.opencv.core  mutableMapOf org.opencv.core  
plusAssign org.opencv.core  set org.opencv.core  sortedByDescending org.opencv.core  take org.opencv.core  until org.opencv.core  withContext org.opencv.core  mean org.opencv.core.Core  	minMaxLoc org.opencv.core.Core  maxVal $org.opencv.core.Core.MinMaxLocResult  CV_8UC3 org.opencv.core.CvType  height org.opencv.core.Mat  release org.opencv.core.Mat  size org.opencv.core.Mat  width org.opencv.core.Mat  val org.opencv.core.Scalar  Imgproc org.opencv.imgproc  COLOR_RGB2GRAY org.opencv.imgproc.Imgproc  COLOR_RGBA2RGB org.opencv.imgproc.Imgproc  TM_CCOEFF_NORMED org.opencv.imgproc.Imgproc  cvtColor org.opencv.imgproc.Imgproc  
matchTemplate org.opencv.imgproc.Imgproc  resize org.opencv.imgproc.Imgproc                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                      