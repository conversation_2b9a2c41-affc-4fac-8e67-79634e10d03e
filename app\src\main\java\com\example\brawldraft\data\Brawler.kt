package com.example.brawldraft.data

data class Brawler(
    val id: String,
    val name: String,
    val rarity: BrawlerRarity,
    val type: BrawlerType,
    val description: String,
    val iconResourceName: String? = null,
    val winRate: Float = 0.0f,
    val pickRate: Float = 0.0f,
    val banRate: Float = 0.0f
) {
    companion object {
        // Create a brawler instance for unknown/undetected brawlers
        fun unknown(id: String = "unknown") = Brawler(
            id = id,
            name = "Unknown",
            rarity = BrawlerRarity.COMMON,
            type = BrawlerType.DAMAGE_DEALER,
            description = "Undetected brawler"
        )
    }
}

enum class BrawlerRarity {
    COMMON,
    RARE,
    SUPER_RARE,
    EPIC,
    MYTHIC,
    LEGENDARY,
    CHROMATIC
}

enum class BrawlerType {
    TANK,
    DAMAGE_DEALER,
    SUPPORT,
    ASSASSIN,
    MARKSMAN,
    ARTILLERY,
    CONTROLLER
}

// Team position in draft
enum class TeamSide {
    BLUE,  // Your team
    RED    // Enemy team
}

// Draft phase types
enum class DraftPhase {
    WAITING,           // Before draft starts
    BAN_PHASE,         // Simultaneous ban phase
    PICK_PHASE_1,      // First pick phase (A picks 1)
    PICK_PHASE_2,      // Second pick phase (B picks 2)
    PICK_PHASE_3,      // Third pick phase (A picks 2)
    PICK_PHASE_4,      // Fourth pick phase (B picks 1)
    COMPLETED          // Draft finished
}

// Individual pick/ban action
data class DraftAction(
    val brawler: Brawler,
    val team: TeamSide,
    val actionType: ActionType,
    val phase: DraftPhase,
    val timestamp: Long = System.currentTimeMillis()
)

enum class ActionType {
    BAN,
    PICK
}

// Map information
data class GameMap(
    val id: String,
    val name: String,
    val mode: GameMode,
    val description: String = "",
    val iconResourceName: String? = null
) {
    companion object {
        fun unknown() = GameMap(
            id = "unknown",
            name = "Unknown Map",
            mode = GameMode.UNKNOWN
        )
    }
}

enum class GameMode {
    GEM_GRAB,
    SHOWDOWN,
    BRAWL_BALL,
    BOUNTY,
    HEIST,
    SIEGE,
    HOT_ZONE,
    KNOCKOUT,
    UNKNOWN
}
