1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.example.brawldraft"
4    android:versionCode="1"
5    android:versionName="1.0" >
6
7    <uses-sdk
8        android:minSdkVersion="24"
9        android:targetSdkVersion="36" />
10
11    <uses-permission android:name="android.permission.SYSTEM_ALERT_WINDOW" />
11-->C:\Users\<USER>\AndroidStudioProjects\Brawldraft\app\src\main\AndroidManifest.xml:5:5-78
11-->C:\Users\<USER>\AndroidStudioProjects\Brawldraft\app\src\main\AndroidManifest.xml:5:22-75
12    <uses-permission android:name="android.permission.FOREGROUND_SERVICE" />
12-->C:\Users\<USER>\AndroidStudioProjects\Brawldraft\app\src\main\AndroidManifest.xml:6:5-77
12-->C:\Users\<USER>\AndroidStudioProjects\Brawldraft\app\src\main\AndroidManifest.xml:6:22-74
13    <uses-permission android:name="android.permission.FOREGROUND_SERVICE_MEDIA_PROJECTION" />
13-->C:\Users\<USER>\AndroidStudioProjects\Brawldraft\app\src\main\AndroidManifest.xml:7:5-94
13-->C:\Users\<USER>\AndroidStudioProjects\Brawldraft\app\src\main\AndroidManifest.xml:7:22-91
14
15    <permission
15-->[androidx.core:core:1.10.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\27d9032238570e73400cc8465b116304\transformed\core-1.10.1\AndroidManifest.xml:22:5-24:47
16        android:name="com.example.brawldraft.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
16-->[androidx.core:core:1.10.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\27d9032238570e73400cc8465b116304\transformed\core-1.10.1\AndroidManifest.xml:23:9-81
17        android:protectionLevel="signature" />
17-->[androidx.core:core:1.10.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\27d9032238570e73400cc8465b116304\transformed\core-1.10.1\AndroidManifest.xml:24:9-44
18
19    <uses-permission android:name="com.example.brawldraft.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
19-->[androidx.core:core:1.10.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\27d9032238570e73400cc8465b116304\transformed\core-1.10.1\AndroidManifest.xml:26:5-97
19-->[androidx.core:core:1.10.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\27d9032238570e73400cc8465b116304\transformed\core-1.10.1\AndroidManifest.xml:26:22-94
20
21    <application
21-->C:\Users\<USER>\AndroidStudioProjects\Brawldraft\app\src\main\AndroidManifest.xml:9:5-39:19
22        android:allowBackup="true"
22-->C:\Users\<USER>\AndroidStudioProjects\Brawldraft\app\src\main\AndroidManifest.xml:10:9-35
23        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
23-->[androidx.core:core:1.10.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\27d9032238570e73400cc8465b116304\transformed\core-1.10.1\AndroidManifest.xml:28:18-86
24        android:dataExtractionRules="@xml/data_extraction_rules"
24-->C:\Users\<USER>\AndroidStudioProjects\Brawldraft\app\src\main\AndroidManifest.xml:11:9-65
25        android:debuggable="true"
26        android:extractNativeLibs="false"
27        android:fullBackupContent="@xml/backup_rules"
27-->C:\Users\<USER>\AndroidStudioProjects\Brawldraft\app\src\main\AndroidManifest.xml:12:9-54
28        android:icon="@mipmap/ic_launcher"
28-->C:\Users\<USER>\AndroidStudioProjects\Brawldraft\app\src\main\AndroidManifest.xml:13:9-43
29        android:label="@string/app_name"
29-->C:\Users\<USER>\AndroidStudioProjects\Brawldraft\app\src\main\AndroidManifest.xml:14:9-41
30        android:roundIcon="@mipmap/ic_launcher_round"
30-->C:\Users\<USER>\AndroidStudioProjects\Brawldraft\app\src\main\AndroidManifest.xml:15:9-54
31        android:supportsRtl="true"
31-->C:\Users\<USER>\AndroidStudioProjects\Brawldraft\app\src\main\AndroidManifest.xml:16:9-35
32        android:theme="@style/Theme.Brawldraft" >
32-->C:\Users\<USER>\AndroidStudioProjects\Brawldraft\app\src\main\AndroidManifest.xml:17:9-48
33        <activity
33-->C:\Users\<USER>\AndroidStudioProjects\Brawldraft\app\src\main\AndroidManifest.xml:18:9-26:20
34            android:name="com.example.brawldraft.MainActivity"
34-->C:\Users\<USER>\AndroidStudioProjects\Brawldraft\app\src\main\AndroidManifest.xml:19:13-41
35            android:exported="true" >
35-->C:\Users\<USER>\AndroidStudioProjects\Brawldraft\app\src\main\AndroidManifest.xml:20:13-36
36            <intent-filter>
36-->C:\Users\<USER>\AndroidStudioProjects\Brawldraft\app\src\main\AndroidManifest.xml:21:13-25:29
37                <action android:name="android.intent.action.MAIN" />
37-->C:\Users\<USER>\AndroidStudioProjects\Brawldraft\app\src\main\AndroidManifest.xml:22:17-69
37-->C:\Users\<USER>\AndroidStudioProjects\Brawldraft\app\src\main\AndroidManifest.xml:22:25-66
38
39                <category android:name="android.intent.category.LAUNCHER" />
39-->C:\Users\<USER>\AndroidStudioProjects\Brawldraft\app\src\main\AndroidManifest.xml:24:17-77
39-->C:\Users\<USER>\AndroidStudioProjects\Brawldraft\app\src\main\AndroidManifest.xml:24:27-74
40            </intent-filter>
41        </activity>
42        <activity
42-->C:\Users\<USER>\AndroidStudioProjects\Brawldraft\app\src\main\AndroidManifest.xml:28:9-31:40
43            android:name="com.example.brawldraft.ScreenCapturePermissionActivity"
43-->C:\Users\<USER>\AndroidStudioProjects\Brawldraft\app\src\main\AndroidManifest.xml:29:13-60
44            android:exported="false"
44-->C:\Users\<USER>\AndroidStudioProjects\Brawldraft\app\src\main\AndroidManifest.xml:31:13-37
45            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
45-->C:\Users\<USER>\AndroidStudioProjects\Brawldraft\app\src\main\AndroidManifest.xml:30:13-72
46
47        <service android:name="com.example.brawldraft.FloatingOverlayService" />
47-->C:\Users\<USER>\AndroidStudioProjects\Brawldraft\app\src\main\AndroidManifest.xml:33:9-59
47-->C:\Users\<USER>\AndroidStudioProjects\Brawldraft\app\src\main\AndroidManifest.xml:33:18-56
48        <service
48-->C:\Users\<USER>\AndroidStudioProjects\Brawldraft\app\src\main\AndroidManifest.xml:35:9-37:63
49            android:name="com.example.brawldraft.ScreenCaptureService"
49-->C:\Users\<USER>\AndroidStudioProjects\Brawldraft\app\src\main\AndroidManifest.xml:36:13-49
50            android:foregroundServiceType="mediaProjection" />
50-->C:\Users\<USER>\AndroidStudioProjects\Brawldraft\app\src\main\AndroidManifest.xml:37:13-60
51
52        <provider
52-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\329078bde69d4dba3eee3c51fa507581\transformed\emoji2-1.2.0\AndroidManifest.xml:24:9-32:20
53            android:name="androidx.startup.InitializationProvider"
53-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\329078bde69d4dba3eee3c51fa507581\transformed\emoji2-1.2.0\AndroidManifest.xml:25:13-67
54            android:authorities="com.example.brawldraft.androidx-startup"
54-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\329078bde69d4dba3eee3c51fa507581\transformed\emoji2-1.2.0\AndroidManifest.xml:26:13-68
55            android:exported="false" >
55-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\329078bde69d4dba3eee3c51fa507581\transformed\emoji2-1.2.0\AndroidManifest.xml:27:13-37
56            <meta-data
56-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\329078bde69d4dba3eee3c51fa507581\transformed\emoji2-1.2.0\AndroidManifest.xml:29:13-31:52
57                android:name="androidx.emoji2.text.EmojiCompatInitializer"
57-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\329078bde69d4dba3eee3c51fa507581\transformed\emoji2-1.2.0\AndroidManifest.xml:30:17-75
58                android:value="androidx.startup" />
58-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\329078bde69d4dba3eee3c51fa507581\transformed\emoji2-1.2.0\AndroidManifest.xml:31:17-49
59            <meta-data
59-->[androidx.lifecycle:lifecycle-process:2.6.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\b158b40abb2bffb0911c41424820a72c\transformed\lifecycle-process-2.6.1\AndroidManifest.xml:29:13-31:52
60                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
60-->[androidx.lifecycle:lifecycle-process:2.6.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\b158b40abb2bffb0911c41424820a72c\transformed\lifecycle-process-2.6.1\AndroidManifest.xml:30:17-78
61                android:value="androidx.startup" />
61-->[androidx.lifecycle:lifecycle-process:2.6.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\b158b40abb2bffb0911c41424820a72c\transformed\lifecycle-process-2.6.1\AndroidManifest.xml:31:17-49
62            <meta-data
62-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\efb3be5c26ff3e55d07c351acc9bddda\transformed\profileinstaller-1.3.0\AndroidManifest.xml:29:13-31:52
63                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
63-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\efb3be5c26ff3e55d07c351acc9bddda\transformed\profileinstaller-1.3.0\AndroidManifest.xml:30:17-85
64                android:value="androidx.startup" />
64-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\efb3be5c26ff3e55d07c351acc9bddda\transformed\profileinstaller-1.3.0\AndroidManifest.xml:31:17-49
65        </provider>
66
67        <receiver
67-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\efb3be5c26ff3e55d07c351acc9bddda\transformed\profileinstaller-1.3.0\AndroidManifest.xml:34:9-52:20
68            android:name="androidx.profileinstaller.ProfileInstallReceiver"
68-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\efb3be5c26ff3e55d07c351acc9bddda\transformed\profileinstaller-1.3.0\AndroidManifest.xml:35:13-76
69            android:directBootAware="false"
69-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\efb3be5c26ff3e55d07c351acc9bddda\transformed\profileinstaller-1.3.0\AndroidManifest.xml:36:13-44
70            android:enabled="true"
70-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\efb3be5c26ff3e55d07c351acc9bddda\transformed\profileinstaller-1.3.0\AndroidManifest.xml:37:13-35
71            android:exported="true"
71-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\efb3be5c26ff3e55d07c351acc9bddda\transformed\profileinstaller-1.3.0\AndroidManifest.xml:38:13-36
72            android:permission="android.permission.DUMP" >
72-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\efb3be5c26ff3e55d07c351acc9bddda\transformed\profileinstaller-1.3.0\AndroidManifest.xml:39:13-57
73            <intent-filter>
73-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\efb3be5c26ff3e55d07c351acc9bddda\transformed\profileinstaller-1.3.0\AndroidManifest.xml:40:13-42:29
74                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
74-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\efb3be5c26ff3e55d07c351acc9bddda\transformed\profileinstaller-1.3.0\AndroidManifest.xml:41:17-91
74-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\efb3be5c26ff3e55d07c351acc9bddda\transformed\profileinstaller-1.3.0\AndroidManifest.xml:41:25-88
75            </intent-filter>
76            <intent-filter>
76-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\efb3be5c26ff3e55d07c351acc9bddda\transformed\profileinstaller-1.3.0\AndroidManifest.xml:43:13-45:29
77                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
77-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\efb3be5c26ff3e55d07c351acc9bddda\transformed\profileinstaller-1.3.0\AndroidManifest.xml:44:17-85
77-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\efb3be5c26ff3e55d07c351acc9bddda\transformed\profileinstaller-1.3.0\AndroidManifest.xml:44:25-82
78            </intent-filter>
79            <intent-filter>
79-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\efb3be5c26ff3e55d07c351acc9bddda\transformed\profileinstaller-1.3.0\AndroidManifest.xml:46:13-48:29
80                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
80-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\efb3be5c26ff3e55d07c351acc9bddda\transformed\profileinstaller-1.3.0\AndroidManifest.xml:47:17-88
80-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\efb3be5c26ff3e55d07c351acc9bddda\transformed\profileinstaller-1.3.0\AndroidManifest.xml:47:25-85
81            </intent-filter>
82            <intent-filter>
82-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\efb3be5c26ff3e55d07c351acc9bddda\transformed\profileinstaller-1.3.0\AndroidManifest.xml:49:13-51:29
83                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
83-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\efb3be5c26ff3e55d07c351acc9bddda\transformed\profileinstaller-1.3.0\AndroidManifest.xml:50:17-95
83-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\efb3be5c26ff3e55d07c351acc9bddda\transformed\profileinstaller-1.3.0\AndroidManifest.xml:50:25-92
84            </intent-filter>
85        </receiver>
86    </application>
87
88</manifest>
