1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.example.brawldraft"
4    android:versionCode="1"
5    android:versionName="1.0" >
6
7    <uses-sdk
8        android:minSdkVersion="24"
9        android:targetSdkVersion="36" />
10
11    <uses-permission android:name="android.permission.SYSTEM_ALERT_WINDOW" />
11-->C:\Users\<USER>\AndroidStudioProjects\Brawldraft\app\src\main\AndroidManifest.xml:5:5-78
11-->C:\Users\<USER>\AndroidStudioProjects\Brawldraft\app\src\main\AndroidManifest.xml:5:22-75
12    <uses-permission android:name="android.permission.FOREGROUND_SERVICE" />
12-->C:\Users\<USER>\AndroidStudioProjects\Brawldraft\app\src\main\AndroidManifest.xml:6:5-77
12-->C:\Users\<USER>\AndroidStudioProjects\Brawldraft\app\src\main\AndroidManifest.xml:6:22-74
13    <uses-permission android:name="android.permission.FOREGROUND_SERVICE_MEDIA_PROJECTION" />
13-->C:\Users\<USER>\AndroidStudioProjects\Brawldraft\app\src\main\AndroidManifest.xml:7:5-94
13-->C:\Users\<USER>\AndroidStudioProjects\Brawldraft\app\src\main\AndroidManifest.xml:7:22-91
14
15    <permission
15-->[androidx.core:core:1.10.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\27d9032238570e73400cc8465b116304\transformed\core-1.10.1\AndroidManifest.xml:22:5-24:47
16        android:name="com.example.brawldraft.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
16-->[androidx.core:core:1.10.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\27d9032238570e73400cc8465b116304\transformed\core-1.10.1\AndroidManifest.xml:23:9-81
17        android:protectionLevel="signature" />
17-->[androidx.core:core:1.10.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\27d9032238570e73400cc8465b116304\transformed\core-1.10.1\AndroidManifest.xml:24:9-44
18
19    <uses-permission android:name="com.example.brawldraft.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
19-->[androidx.core:core:1.10.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\27d9032238570e73400cc8465b116304\transformed\core-1.10.1\AndroidManifest.xml:26:5-97
19-->[androidx.core:core:1.10.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\27d9032238570e73400cc8465b116304\transformed\core-1.10.1\AndroidManifest.xml:26:22-94
20
21    <application
21-->C:\Users\<USER>\AndroidStudioProjects\Brawldraft\app\src\main\AndroidManifest.xml:9:5-39:19
22        android:allowBackup="true"
22-->C:\Users\<USER>\AndroidStudioProjects\Brawldraft\app\src\main\AndroidManifest.xml:10:9-35
23        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
23-->[androidx.core:core:1.10.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\27d9032238570e73400cc8465b116304\transformed\core-1.10.1\AndroidManifest.xml:28:18-86
24        android:dataExtractionRules="@xml/data_extraction_rules"
24-->C:\Users\<USER>\AndroidStudioProjects\Brawldraft\app\src\main\AndroidManifest.xml:11:9-65
25        android:debuggable="true"
26        android:extractNativeLibs="false"
27        android:fullBackupContent="@xml/backup_rules"
27-->C:\Users\<USER>\AndroidStudioProjects\Brawldraft\app\src\main\AndroidManifest.xml:12:9-54
28        android:icon="@mipmap/ic_launcher"
28-->C:\Users\<USER>\AndroidStudioProjects\Brawldraft\app\src\main\AndroidManifest.xml:13:9-43
29        android:label="@string/app_name"
29-->C:\Users\<USER>\AndroidStudioProjects\Brawldraft\app\src\main\AndroidManifest.xml:14:9-41
30        android:roundIcon="@mipmap/ic_launcher_round"
30-->C:\Users\<USER>\AndroidStudioProjects\Brawldraft\app\src\main\AndroidManifest.xml:15:9-54
31        android:supportsRtl="true"
31-->C:\Users\<USER>\AndroidStudioProjects\Brawldraft\app\src\main\AndroidManifest.xml:16:9-35
32        android:testOnly="true"
33        android:theme="@style/Theme.Brawldraft" >
33-->C:\Users\<USER>\AndroidStudioProjects\Brawldraft\app\src\main\AndroidManifest.xml:17:9-48
34        <activity
34-->C:\Users\<USER>\AndroidStudioProjects\Brawldraft\app\src\main\AndroidManifest.xml:18:9-26:20
35            android:name="com.example.brawldraft.MainActivity"
35-->C:\Users\<USER>\AndroidStudioProjects\Brawldraft\app\src\main\AndroidManifest.xml:19:13-41
36            android:exported="true" >
36-->C:\Users\<USER>\AndroidStudioProjects\Brawldraft\app\src\main\AndroidManifest.xml:20:13-36
37            <intent-filter>
37-->C:\Users\<USER>\AndroidStudioProjects\Brawldraft\app\src\main\AndroidManifest.xml:21:13-25:29
38                <action android:name="android.intent.action.MAIN" />
38-->C:\Users\<USER>\AndroidStudioProjects\Brawldraft\app\src\main\AndroidManifest.xml:22:17-69
38-->C:\Users\<USER>\AndroidStudioProjects\Brawldraft\app\src\main\AndroidManifest.xml:22:25-66
39
40                <category android:name="android.intent.category.LAUNCHER" />
40-->C:\Users\<USER>\AndroidStudioProjects\Brawldraft\app\src\main\AndroidManifest.xml:24:17-77
40-->C:\Users\<USER>\AndroidStudioProjects\Brawldraft\app\src\main\AndroidManifest.xml:24:27-74
41            </intent-filter>
42        </activity>
43        <activity
43-->C:\Users\<USER>\AndroidStudioProjects\Brawldraft\app\src\main\AndroidManifest.xml:28:9-31:40
44            android:name="com.example.brawldraft.ScreenCapturePermissionActivity"
44-->C:\Users\<USER>\AndroidStudioProjects\Brawldraft\app\src\main\AndroidManifest.xml:29:13-60
45            android:exported="false"
45-->C:\Users\<USER>\AndroidStudioProjects\Brawldraft\app\src\main\AndroidManifest.xml:31:13-37
46            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
46-->C:\Users\<USER>\AndroidStudioProjects\Brawldraft\app\src\main\AndroidManifest.xml:30:13-72
47
48        <service android:name="com.example.brawldraft.FloatingOverlayService" />
48-->C:\Users\<USER>\AndroidStudioProjects\Brawldraft\app\src\main\AndroidManifest.xml:33:9-59
48-->C:\Users\<USER>\AndroidStudioProjects\Brawldraft\app\src\main\AndroidManifest.xml:33:18-56
49        <service
49-->C:\Users\<USER>\AndroidStudioProjects\Brawldraft\app\src\main\AndroidManifest.xml:35:9-37:63
50            android:name="com.example.brawldraft.ScreenCaptureService"
50-->C:\Users\<USER>\AndroidStudioProjects\Brawldraft\app\src\main\AndroidManifest.xml:36:13-49
51            android:foregroundServiceType="mediaProjection" />
51-->C:\Users\<USER>\AndroidStudioProjects\Brawldraft\app\src\main\AndroidManifest.xml:37:13-60
52
53        <provider
53-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\329078bde69d4dba3eee3c51fa507581\transformed\emoji2-1.2.0\AndroidManifest.xml:24:9-32:20
54            android:name="androidx.startup.InitializationProvider"
54-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\329078bde69d4dba3eee3c51fa507581\transformed\emoji2-1.2.0\AndroidManifest.xml:25:13-67
55            android:authorities="com.example.brawldraft.androidx-startup"
55-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\329078bde69d4dba3eee3c51fa507581\transformed\emoji2-1.2.0\AndroidManifest.xml:26:13-68
56            android:exported="false" >
56-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\329078bde69d4dba3eee3c51fa507581\transformed\emoji2-1.2.0\AndroidManifest.xml:27:13-37
57            <meta-data
57-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\329078bde69d4dba3eee3c51fa507581\transformed\emoji2-1.2.0\AndroidManifest.xml:29:13-31:52
58                android:name="androidx.emoji2.text.EmojiCompatInitializer"
58-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\329078bde69d4dba3eee3c51fa507581\transformed\emoji2-1.2.0\AndroidManifest.xml:30:17-75
59                android:value="androidx.startup" />
59-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\329078bde69d4dba3eee3c51fa507581\transformed\emoji2-1.2.0\AndroidManifest.xml:31:17-49
60            <meta-data
60-->[androidx.lifecycle:lifecycle-process:2.6.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\b158b40abb2bffb0911c41424820a72c\transformed\lifecycle-process-2.6.1\AndroidManifest.xml:29:13-31:52
61                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
61-->[androidx.lifecycle:lifecycle-process:2.6.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\b158b40abb2bffb0911c41424820a72c\transformed\lifecycle-process-2.6.1\AndroidManifest.xml:30:17-78
62                android:value="androidx.startup" />
62-->[androidx.lifecycle:lifecycle-process:2.6.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\b158b40abb2bffb0911c41424820a72c\transformed\lifecycle-process-2.6.1\AndroidManifest.xml:31:17-49
63            <meta-data
63-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\efb3be5c26ff3e55d07c351acc9bddda\transformed\profileinstaller-1.3.0\AndroidManifest.xml:29:13-31:52
64                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
64-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\efb3be5c26ff3e55d07c351acc9bddda\transformed\profileinstaller-1.3.0\AndroidManifest.xml:30:17-85
65                android:value="androidx.startup" />
65-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\efb3be5c26ff3e55d07c351acc9bddda\transformed\profileinstaller-1.3.0\AndroidManifest.xml:31:17-49
66        </provider>
67
68        <receiver
68-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\efb3be5c26ff3e55d07c351acc9bddda\transformed\profileinstaller-1.3.0\AndroidManifest.xml:34:9-52:20
69            android:name="androidx.profileinstaller.ProfileInstallReceiver"
69-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\efb3be5c26ff3e55d07c351acc9bddda\transformed\profileinstaller-1.3.0\AndroidManifest.xml:35:13-76
70            android:directBootAware="false"
70-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\efb3be5c26ff3e55d07c351acc9bddda\transformed\profileinstaller-1.3.0\AndroidManifest.xml:36:13-44
71            android:enabled="true"
71-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\efb3be5c26ff3e55d07c351acc9bddda\transformed\profileinstaller-1.3.0\AndroidManifest.xml:37:13-35
72            android:exported="true"
72-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\efb3be5c26ff3e55d07c351acc9bddda\transformed\profileinstaller-1.3.0\AndroidManifest.xml:38:13-36
73            android:permission="android.permission.DUMP" >
73-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\efb3be5c26ff3e55d07c351acc9bddda\transformed\profileinstaller-1.3.0\AndroidManifest.xml:39:13-57
74            <intent-filter>
74-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\efb3be5c26ff3e55d07c351acc9bddda\transformed\profileinstaller-1.3.0\AndroidManifest.xml:40:13-42:29
75                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
75-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\efb3be5c26ff3e55d07c351acc9bddda\transformed\profileinstaller-1.3.0\AndroidManifest.xml:41:17-91
75-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\efb3be5c26ff3e55d07c351acc9bddda\transformed\profileinstaller-1.3.0\AndroidManifest.xml:41:25-88
76            </intent-filter>
77            <intent-filter>
77-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\efb3be5c26ff3e55d07c351acc9bddda\transformed\profileinstaller-1.3.0\AndroidManifest.xml:43:13-45:29
78                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
78-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\efb3be5c26ff3e55d07c351acc9bddda\transformed\profileinstaller-1.3.0\AndroidManifest.xml:44:17-85
78-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\efb3be5c26ff3e55d07c351acc9bddda\transformed\profileinstaller-1.3.0\AndroidManifest.xml:44:25-82
79            </intent-filter>
80            <intent-filter>
80-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\efb3be5c26ff3e55d07c351acc9bddda\transformed\profileinstaller-1.3.0\AndroidManifest.xml:46:13-48:29
81                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
81-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\efb3be5c26ff3e55d07c351acc9bddda\transformed\profileinstaller-1.3.0\AndroidManifest.xml:47:17-88
81-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\efb3be5c26ff3e55d07c351acc9bddda\transformed\profileinstaller-1.3.0\AndroidManifest.xml:47:25-85
82            </intent-filter>
83            <intent-filter>
83-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\efb3be5c26ff3e55d07c351acc9bddda\transformed\profileinstaller-1.3.0\AndroidManifest.xml:49:13-51:29
84                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
84-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\efb3be5c26ff3e55d07c351acc9bddda\transformed\profileinstaller-1.3.0\AndroidManifest.xml:50:17-95
84-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\efb3be5c26ff3e55d07c351acc9bddda\transformed\profileinstaller-1.3.0\AndroidManifest.xml:50:25-92
85            </intent-filter>
86        </receiver>
87    </application>
88
89</manifest>
