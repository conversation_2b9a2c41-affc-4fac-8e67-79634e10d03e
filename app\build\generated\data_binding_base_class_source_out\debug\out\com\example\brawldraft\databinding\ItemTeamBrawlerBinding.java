// Generated by view binder compiler. Do not edit!
package com.example.brawldraft.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.LinearLayout;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.example.brawldraft.R;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class ItemTeamBrawlerBinding implements ViewBinding {
  @NonNull
  private final LinearLayout rootView;

  @NonNull
  public final TextView actionType;

  @NonNull
  public final TextView brawlerName;

  @NonNull
  public final View statusIndicator;

  private ItemTeamBrawlerBinding(@NonNull LinearLayout rootView, @NonNull TextView actionType,
      @NonNull TextView brawlerName, @NonNull View statusIndicator) {
    this.rootView = rootView;
    this.actionType = actionType;
    this.brawlerName = brawlerName;
    this.statusIndicator = statusIndicator;
  }

  @Override
  @NonNull
  public LinearLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static ItemTeamBrawlerBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static ItemTeamBrawlerBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.item_team_brawler, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static ItemTeamBrawlerBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.action_type;
      TextView actionType = ViewBindings.findChildViewById(rootView, id);
      if (actionType == null) {
        break missingId;
      }

      id = R.id.brawler_name;
      TextView brawlerName = ViewBindings.findChildViewById(rootView, id);
      if (brawlerName == null) {
        break missingId;
      }

      id = R.id.status_indicator;
      View statusIndicator = ViewBindings.findChildViewById(rootView, id);
      if (statusIndicator == null) {
        break missingId;
      }

      return new ItemTeamBrawlerBinding((LinearLayout) rootView, actionType, brawlerName,
          statusIndicator);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
