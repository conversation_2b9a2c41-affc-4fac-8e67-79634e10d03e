{"logs": [{"outputFile": "com.example.brawldraft.app-mergeDebugResources-30:/values-sq/values-sq.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\57562ad43921b16639a6438f83b538f4\\transformed\\appcompat-1.6.1\\res\\values-sq\\values-sq.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,219,319,431,517,623,746,828,906,997,1090,1185,1279,1380,1473,1568,1665,1756,1849,1930,2036,2140,2238,2344,2448,2550,2704,2801", "endColumns": "113,99,111,85,105,122,81,77,90,92,94,93,100,92,94,96,90,92,80,105,103,97,105,103,101,153,96,81", "endOffsets": "214,314,426,512,618,741,823,901,992,1085,1180,1274,1375,1468,1563,1660,1751,1844,1925,2031,2135,2233,2339,2443,2545,2699,2796,2878"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,108", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "317,431,531,643,729,835,958,1040,1118,1209,1302,1397,1491,1592,1685,1780,1877,1968,2061,2142,2248,2352,2450,2556,2660,2762,2916,9327", "endColumns": "113,99,111,85,105,122,81,77,90,92,94,93,100,92,94,96,90,92,80,105,103,97,105,103,101,153,96,81", "endOffsets": "426,526,638,724,830,953,1035,1113,1204,1297,1392,1486,1587,1680,1775,1872,1963,2056,2137,2243,2347,2445,2551,2655,2757,2911,3008,9404"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\2aa33be5c222f8571691eafceec0feae\\transformed\\material-1.10.0\\res\\values-sq\\values-sq.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,267,346,424,510,610,702,803,929,1012,1077,1177,1247,1306,1404,1466,1530,1589,1661,1724,1778,1895,1952,2014,2068,2140,2275,2358,2436,2577,2661,2743,2891,2981,3059,3112,3171,3237,3308,3387,3475,3558,3634,3712,3784,3857,3961,4050,4122,4216,4315,4389,4461,4562,4612,4697,4763,4853,4942,5004,5068,5131,5198,5314,5427,5536,5641,5698,5761", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73", "endColumns": "12,78,77,85,99,91,100,125,82,64,99,69,58,97,61,63,58,71,62,53,116,56,61,53,71,134,82,77,140,83,81,147,89,77,52,58,65,70,78,87,82,75,77,71,72,103,88,71,93,98,73,71,100,49,84,65,89,88,61,63,62,66,115,112,108,104,56,62,82", "endOffsets": "262,341,419,505,605,697,798,924,1007,1072,1172,1242,1301,1399,1461,1525,1584,1656,1719,1773,1890,1947,2009,2063,2135,2270,2353,2431,2572,2656,2738,2886,2976,3054,3107,3166,3232,3303,3382,3470,3553,3629,3707,3779,3852,3956,4045,4117,4211,4310,4384,4456,4557,4607,4692,4758,4848,4937,4999,5063,5126,5193,5309,5422,5531,5636,5693,5756,5839"}, "to": {"startLines": "2,33,34,35,36,37,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,3013,3092,3170,3256,3356,4185,4286,4412,4495,4560,4660,4730,4789,4887,4949,5013,5072,5144,5207,5261,5378,5435,5497,5551,5623,5758,5841,5919,6060,6144,6226,6374,6464,6542,6595,6654,6720,6791,6870,6958,7041,7117,7195,7267,7340,7444,7533,7605,7699,7798,7872,7944,8045,8095,8180,8246,8336,8425,8487,8551,8614,8681,8797,8910,9019,9124,9181,9244", "endLines": "5,33,34,35,36,37,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107", "endColumns": "12,78,77,85,99,91,100,125,82,64,99,69,58,97,61,63,58,71,62,53,116,56,61,53,71,134,82,77,140,83,81,147,89,77,52,58,65,70,78,87,82,75,77,71,72,103,88,71,93,98,73,71,100,49,84,65,89,88,61,63,62,66,115,112,108,104,56,62,82", "endOffsets": "312,3087,3165,3251,3351,3443,4281,4407,4490,4555,4655,4725,4784,4882,4944,5008,5067,5139,5202,5256,5373,5430,5492,5546,5618,5753,5836,5914,6055,6139,6221,6369,6459,6537,6590,6649,6715,6786,6865,6953,7036,7112,7190,7262,7335,7439,7528,7600,7694,7793,7867,7939,8040,8090,8175,8241,8331,8420,8482,8546,8609,8676,8792,8905,9014,9119,9176,9239,9322"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\27d9032238570e73400cc8465b116304\\transformed\\core-1.10.1\\res\\values-sq\\values-sq.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,154,256,354,451,559,670,792", "endColumns": "98,101,97,96,107,110,121,100", "endOffsets": "149,251,349,446,554,665,787,888"}, "to": {"startLines": "38,39,40,41,42,43,44,109", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3448,3547,3649,3747,3844,3952,4063,9409", "endColumns": "98,101,97,96,107,110,121,100", "endOffsets": "3542,3644,3742,3839,3947,4058,4180,9505"}}]}]}