package com.example.brawldraft.assets

import android.content.Context
import android.graphics.*
import com.example.brawldraft.data.Brawler
import com.example.brawldraft.data.BrawlerDatabase
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import java.io.File
import java.io.FileOutputStream

class PlaceholderGenerator(private val context: Context) {
    
    companion object {
        private const val ICON_SIZE = 128
        private const val TEXT_SIZE = 16f
    }
    
    suspend fun generatePlaceholderIcons() = withContext(Dispatchers.IO) {
        val brawlers = BrawlerDatabase.getAllBrawlers()
        val assetsDir = File(context.filesDir, "generated_assets/brawler_icons")
        
        if (!assetsDir.exists()) {
            assetsDir.mkdirs()
        }
        
        for (brawler in brawlers) {
            generateBrawlerIcon(brawler, assetsDir)
        }
    }
    
    private fun generateBrawlerIcon(brawler: Brawler, outputDir: File) {
        val bitmap = Bitmap.createBitmap(ICON_SIZE, ICON_SIZE, Bitmap.Config.ARGB_8888)
        val canvas = Canvas(bitmap)
        
        // Background color based on rarity
        val backgroundColor = when (brawler.rarity) {
            com.example.brawldraft.data.BrawlerRarity.COMMON -> Color.parseColor("#808080")
            com.example.brawldraft.data.BrawlerRarity.RARE -> Color.parseColor("#00FF00")
            com.example.brawldraft.data.BrawlerRarity.SUPER_RARE -> Color.parseColor("#0080FF")
            com.example.brawldraft.data.BrawlerRarity.EPIC -> Color.parseColor("#8000FF")
            com.example.brawldraft.data.BrawlerRarity.MYTHIC -> Color.parseColor("#FF0080")
            com.example.brawldraft.data.BrawlerRarity.LEGENDARY -> Color.parseColor("#FFD700")
            com.example.brawldraft.data.BrawlerRarity.CHROMATIC -> Color.parseColor("#FF8000")
        }
        
        // Fill background
        canvas.drawColor(backgroundColor)
        
        // Draw border
        val borderPaint = Paint().apply {
            color = Color.WHITE
            style = Paint.Style.STROKE
            strokeWidth = 4f
        }
        canvas.drawRect(2f, 2f, ICON_SIZE - 2f, ICON_SIZE - 2f, borderPaint)
        
        // Draw brawler name
        val textPaint = Paint().apply {
            color = Color.WHITE
            textSize = TEXT_SIZE
            textAlign = Paint.Align.CENTER
            isAntiAlias = true
            typeface = Typeface.DEFAULT_BOLD
        }
        
        // Draw text with shadow for better visibility
        val shadowPaint = Paint().apply {
            color = Color.BLACK
            textSize = TEXT_SIZE
            textAlign = Paint.Align.CENTER
            isAntiAlias = true
            typeface = Typeface.DEFAULT_BOLD
        }
        
        val centerX = ICON_SIZE / 2f
        val centerY = ICON_SIZE / 2f + TEXT_SIZE / 2f
        
        // Draw shadow
        canvas.drawText(brawler.name, centerX + 1, centerY + 1, shadowPaint)
        // Draw main text
        canvas.drawText(brawler.name, centerX, centerY, textPaint)
        
        // Draw type indicator
        val typeIndicator = when (brawler.type) {
            com.example.brawldraft.data.BrawlerType.TANK -> "T"
            com.example.brawldraft.data.BrawlerType.DAMAGE_DEALER -> "D"
            com.example.brawldraft.data.BrawlerType.SUPPORT -> "S"
            com.example.brawldraft.data.BrawlerType.ASSASSIN -> "A"
            com.example.brawldraft.data.BrawlerType.MARKSMAN -> "M"
            com.example.brawldraft.data.BrawlerType.ARTILLERY -> "R"
            com.example.brawldraft.data.BrawlerType.CONTROLLER -> "C"
        }
        
        val typeTextPaint = Paint().apply {
            color = Color.YELLOW
            textSize = 12f
            textAlign = Paint.Align.CENTER
            isAntiAlias = true
            typeface = Typeface.DEFAULT_BOLD
        }
        
        canvas.drawText(typeIndicator, ICON_SIZE - 15f, 20f, typeTextPaint)
        
        // Save to file
        val file = File(outputDir, "${brawler.id}.png")
        try {
            val outputStream = FileOutputStream(file)
            bitmap.compress(Bitmap.CompressFormat.PNG, 100, outputStream)
            outputStream.close()
        } catch (e: Exception) {
            e.printStackTrace()
        }
        
        bitmap.recycle()
    }
}
