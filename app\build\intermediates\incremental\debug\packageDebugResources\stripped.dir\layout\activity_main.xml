<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/gaming_dark_bg"
    android:orientation="vertical"
    android:padding="24dp"
    tools:context=".MainActivity">

    <TextView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="Brawl Stars Draft Helper"
        android:textColor="@color/gaming_text_primary"
        android:textSize="24sp"
        android:textStyle="bold"
        android:gravity="center"
        android:layout_marginBottom="16dp" />

    <TextView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="Real-time draft assistance for ranked matches"
        android:textColor="@color/gaming_text_secondary"
        android:textSize="16sp"
        android:gravity="center"
        android:layout_marginBottom="32dp" />

    <androidx.cardview.widget.CardView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginBottom="24dp"
        app:cardBackgroundColor="@color/gaming_card_bg"
        app:cardCornerRadius="12dp"
        app:cardElevation="4dp">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:padding="16dp">

            <TextView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="Setup Instructions"
                android:textColor="@color/gaming_text_primary"
                android:textSize="18sp"
                android:textStyle="bold"
                android:layout_marginBottom="12dp" />

            <TextView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="1. Grant overlay permission\n2. Launch the floating overlay\n3. Open Brawl Stars and start a ranked match\n4. The overlay will automatically detect the draft"
                android:textColor="@color/gaming_text_secondary"
                android:textSize="14sp"
                android:lineSpacingExtra="4dp" />

        </LinearLayout>

    </androidx.cardview.widget.CardView>

    <Button
        android:id="@+id/launch_overlay_button"
        android:layout_width="match_parent"
        android:layout_height="56dp"
        android:text="Launch Draft Helper Overlay"
        android:textSize="16sp"
        android:backgroundTint="@color/gaming_accent"
        android:layout_marginBottom="16dp" />

    <TextView
        android:id="@+id/status_text"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="Ready to launch overlay"
        android:textColor="@color/gaming_text_secondary"
        android:textSize="14sp"
        android:gravity="center" />

</LinearLayout>