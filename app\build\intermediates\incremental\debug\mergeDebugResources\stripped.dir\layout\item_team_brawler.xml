<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="horizontal"
    android:padding="4dp"
    android:gravity="center_vertical">

    <!-- Status Indicator (picked/banned) -->
    <View
        android:id="@+id/status_indicator"
        android:layout_width="8dp"
        android:layout_height="8dp"
        android:background="@color/gaming_success"
        android:layout_marginEnd="6dp" />

    <!-- Brawler Name -->
    <TextView
        android:id="@+id/brawler_name"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_weight="1"
        android:text="Brawler Name"
        android:textColor="@color/gaming_text_primary"
        android:textSize="12sp" />

    <!-- Action Type (Pick/Ban) -->
    <TextView
        android:id="@+id/action_type"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="P"
        android:textColor="@color/gaming_text_secondary"
        android:textSize="10sp"
        android:textStyle="bold"
        android:background="@color/gaming_card_bg"
        android:padding="2dp"
        android:minWidth="16dp"
        android:gravity="center" />

</LinearLayout>
