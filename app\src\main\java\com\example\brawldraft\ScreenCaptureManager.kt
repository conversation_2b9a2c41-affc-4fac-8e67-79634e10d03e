package com.example.brawldraft

import android.app.Activity
import android.content.Context
import android.content.Intent
import android.media.projection.MediaProjectionManager
import android.util.Log

class ScreenCaptureManager(private val context: Context) {
    
    companion object {
        private const val TAG = "ScreenCaptureManager"
        const val REQUEST_SCREEN_CAPTURE = 1000
    }
    
    private val mediaProjectionManager = context.getSystemService(Context.MEDIA_PROJECTION_SERVICE) as MediaProjectionManager
    
    fun requestScreenCapturePermission(activity: Activity) {
        val captureIntent = mediaProjectionManager.createScreenCaptureIntent()
        activity.startActivityForResult(captureIntent, REQUEST_SCREEN_CAPTURE)
    }
    
    fun startScreenCapture(resultCode: Int, resultData: Intent?) {
        if (resultData == null) {
            Log.e(TAG, "Screen capture permission denied")
            return
        }
        
        val intent = Intent(context, ScreenCaptureService::class.java).apply {
            action = ScreenCaptureService.ACTION_START_CAPTURE
            putExtra(ScreenCaptureService.EXTRA_RESULT_CODE, resultCode)
            putExtra(ScreenCaptureService.EXTRA_RESULT_DATA, resultData)
        }
        
        context.startForegroundService(intent)
        Log.d(TAG, "Screen capture service started")
    }
    
    fun stopScreenCapture() {
        val intent = Intent(context, ScreenCaptureService::class.java).apply {
            action = ScreenCaptureService.ACTION_STOP_CAPTURE
        }
        
        context.startService(intent)
        Log.d(TAG, "Screen capture service stop requested")
    }
}
