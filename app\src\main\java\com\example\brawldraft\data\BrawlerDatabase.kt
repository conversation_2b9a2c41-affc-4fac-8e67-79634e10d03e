package com.example.brawldraft.data

object BrawlerDatabase {
    
    private val brawlers = listOf(
        // Tanks
        Brawler("shelly", "<PERSON><PERSON>", BrawlerRarity.COMMON, BrawlerType.TANK, "Tough shotgunner", winRate = 0.52f),
        <PERSON>rawler("bull", "<PERSON>", BrawlerRarity.RARE, BrawlerType.TANK, "Close-range powerhouse", winRate = 0.48f),
        <PERSON><PERSON><PERSON>("el_primo", "El Primo", BrawlerRarity.RARE, BrawlerType.TANK, "Wrestling champion", winRate = 0.45f),
        <PERSON>rawler("rosa", "<PERSON>", BrawlerRarity.RARE, BrawlerType.TANK, "Botanical brawler", winRate = 0.55f),
        <PERSON>raw<PERSON>("jacky", "<PERSON><PERSON>", BrawlerRarity.SUPER_RARE, BrawlerType.TANK, "Demolition expert", winRate = 0.50f),
        <PERSON><PERSON><PERSON>("frank", "<PERSON>", BrawlerRarity.EPIC, BrawlerType.TAN<PERSON>, "Undead heavyweight", winRate = 0.47f),
        
        // Damage Dealers
        Brawler("colt", "<PERSON>", BrawlerRarity.RARE, BrawlerType.DAMAGE_DEALER, "Sharpshooter", winRate = 0.49f),
        Brawler("brock", "Brock", BrawlerRarity.RARE, BrawlerType.DAMAGE_DEALER, "Rocket launcher", winRate = 0.53f),
        Brawler("rico", "Rico", BrawlerRarity.SUPER_RARE, BrawlerType.DAMAGE_DEALER, "Bouncing bullets", winRate = 0.51f),
        Brawler("8bit", "8-Bit", BrawlerRarity.EPIC, BrawlerType.DAMAGE_DEALER, "Retro arcade", winRate = 0.54f),
        Brawler("emz", "Emz", BrawlerRarity.EPIC, BrawlerType.DAMAGE_DEALER, "Zombie influencer", winRate = 0.56f),
        
        // Support
        Brawler("poco", "Poco", BrawlerRarity.RARE, BrawlerType.SUPPORT, "Musical healer", winRate = 0.58f),
        Brawler("pam", "Pam", BrawlerRarity.EPIC, BrawlerType.SUPPORT, "Scrapyard mechanic", winRate = 0.59f),
        Brawler("gene", "Gene", BrawlerRarity.MYTHIC, BrawlerType.SUPPORT, "Magic lamp keeper", winRate = 0.61f),
        Brawler("byron", "Byron", BrawlerRarity.MYTHIC, BrawlerType.SUPPORT, "Snake oil salesman", winRate = 0.57f),
        
        // Assassins
        Brawler("mortis", "Mortis", BrawlerRarity.MYTHIC, BrawlerType.ASSASSIN, "Dashing gravedigger", winRate = 0.46f),
        Brawler("leon", "Leon", BrawlerRarity.LEGENDARY, BrawlerType.ASSASSIN, "Stealthy ninja", winRate = 0.52f),
        Brawler("crow", "Crow", BrawlerRarity.LEGENDARY, BrawlerType.ASSASSIN, "Poisonous trickster", winRate = 0.50f),
        Brawler("edgar", "Edgar", BrawlerRarity.EPIC, BrawlerType.ASSASSIN, "Parkour artist", winRate = 0.44f),
        
        // Marksmen
        Brawler("piper", "Piper", BrawlerRarity.EPIC, BrawlerType.MARKSMAN, "Long-range sniper", winRate = 0.48f),
        Brawler("belle", "Belle", BrawlerRarity.CHROMATIC, BrawlerType.MARKSMAN, "Goldarm gang leader", winRate = 0.55f),
        Brawler("bea", "Bea", BrawlerRarity.EPIC, BrawlerType.MARKSMAN, "Bee researcher", winRate = 0.53f),
        
        // Artillery
        Brawler("dynamike", "Dynamike", BrawlerRarity.SUPER_RARE, BrawlerType.ARTILLERY, "Explosive miner", winRate = 0.43f),
        Brawler("barley", "Barley", BrawlerRarity.RARE, BrawlerType.ARTILLERY, "Undead bartender", winRate = 0.47f),
        Brawler("tick", "Tick", BrawlerRarity.MYTHIC, BrawlerType.ARTILLERY, "Head-throwing robot", winRate = 0.49f),
        Brawler("sprout", "Sprout", BrawlerRarity.MYTHIC, BrawlerType.ARTILLERY, "Wall-building robot", winRate = 0.52f),
        
        // Controllers
        Brawler("tara", "Tara", BrawlerRarity.MYTHIC, BrawlerType.CONTROLLER, "Fortune teller", winRate = 0.54f),
        Brawler("sandy", "Sandy", BrawlerRarity.LEGENDARY, BrawlerType.CONTROLLER, "Sleepy support", winRate = 0.56f),
        Brawler("amber", "Amber", BrawlerRarity.LEGENDARY, BrawlerType.CONTROLLER, "Fire performer", winRate = 0.51f),
        Brawler("gale", "Gale", BrawlerRarity.CHROMATIC, BrawlerType.CONTROLLER, "Snowplow operator", winRate = 0.53f),
        
        // More recent brawlers (sample)
        Brawler("surge", "Surge", BrawlerRarity.CHROMATIC, BrawlerType.DAMAGE_DEALER, "Juice bar protector", winRate = 0.55f),
        Brawler("colette", "Colette", BrawlerRarity.CHROMATIC, BrawlerType.DAMAGE_DEALER, "Gift shop worker", winRate = 0.49f),
        Brawler("lou", "Lou", BrawlerRarity.CHROMATIC, BrawlerType.CONTROLLER, "Ice cream vendor", winRate = 0.51f),
        Brawler("ruffs", "Colonel Ruffs", BrawlerRarity.CHROMATIC, BrawlerType.SUPPORT, "Space dog commander", winRate = 0.58f),
        Brawler("stu", "Stu", BrawlerRarity.CHROMATIC, BrawlerType.DAMAGE_DEALER, "Stunt performer", winRate = 0.50f),
        Brawler("buzz", "Buzz", BrawlerRarity.CHROMATIC, BrawlerType.ASSASSIN, "Lifeguard", winRate = 0.52f),
        Brawler("griff", "Griff", BrawlerRarity.CHROMATIC, BrawlerType.DAMAGE_DEALER, "Gift shop owner", winRate = 0.54f),
        Brawler("ash", "Ash", BrawlerRarity.CHROMATIC, BrawlerType.TANK, "Trash collector", winRate = 0.49f),
        Brawler("lola", "Lola", BrawlerRarity.CHROMATIC, BrawlerType.DAMAGE_DEALER, "Drama queen", winRate = 0.47f),
        Brawler("meg", "Meg", BrawlerRarity.LEGENDARY, BrawlerType.DAMAGE_DEALER, "Mech pilot", winRate = 0.45f),
        Brawler("grom", "Grom", BrawlerRarity.CHROMATIC, BrawlerType.ARTILLERY, "Radio host", winRate = 0.48f),
        Brawler("fang", "Fang", BrawlerRarity.CHROMATIC, BrawlerType.ASSASSIN, "Kung fu master", winRate = 0.53f),
        Brawler("eve", "Eve", BrawlerRarity.CHROMATIC, BrawlerType.DAMAGE_DEALER, "Queen bee", winRate = 0.56f),
        Brawler("janet", "Janet", BrawlerRarity.CHROMATIC, BrawlerType.DAMAGE_DEALER, "Stunt pilot", winRate = 0.52f),
        Brawler("bonnie", "Bonnie", BrawlerRarity.CHROMATIC, BrawlerType.MARKSMAN, "Cannon operator", winRate = 0.50f),
        Brawler("otis", "Otis", BrawlerRarity.CHROMATIC, BrawlerType.CONTROLLER, "Artist", winRate = 0.54f),
        Brawler("sam", "Sam", BrawlerRarity.CHROMATIC, BrawlerType.ASSASSIN, "Knuckle buster", winRate = 0.48f),
        Brawler("gus", "Gus", BrawlerRarity.CHROMATIC, BrawlerType.SUPPORT, "Ghost", winRate = 0.55f),
        Brawler("buster", "Buster", BrawlerRarity.CHROMATIC, BrawlerType.TANK, "Movie director", winRate = 0.51f),
        Brawler("chester", "Chester", BrawlerRarity.LEGENDARY, BrawlerType.DAMAGE_DEALER, "Jester", winRate = 0.49f),
        Brawler("gray", "Gray", BrawlerRarity.CHROMATIC, BrawlerType.SUPPORT, "Mime", winRate = 0.53f),
        Brawler("mandy", "Mandy", BrawlerRarity.CHROMATIC, BrawlerType.MARKSMAN, "Candy shop owner", winRate = 0.52f),
        Brawler("r-t", "R-T", BrawlerRarity.CHROMATIC, BrawlerType.DAMAGE_DEALER, "Robot duo", winRate = 0.50f),
        Brawler("willow", "Willow", BrawlerRarity.CHROMATIC, BrawlerType.CONTROLLER, "Mind controller", winRate = 0.57f),
        Brawler("maisie", "Maisie", BrawlerRarity.CHROMATIC, BrawlerType.DAMAGE_DEALER, "Demolition expert", winRate = 0.51f)
    )
    
    fun getAllBrawlers(): List<Brawler> = brawlers
    
    fun getBrawlerById(id: String): Brawler? = brawlers.find { it.id == id }
    
    fun getBrawlerByName(name: String): Brawler? = brawlers.find { 
        it.name.equals(name, ignoreCase = true) 
    }
    
    fun getBrawlersByType(type: BrawlerType): List<Brawler> = brawlers.filter { it.type == type }
    
    fun getBrawlersByRarity(rarity: BrawlerRarity): List<Brawler> = brawlers.filter { it.rarity == rarity }
    
    fun getTopBrawlersByWinRate(limit: Int = 10): List<Brawler> = 
        brawlers.sortedByDescending { it.winRate }.take(limit)
    
    fun searchBrawlers(query: String): List<Brawler> = brawlers.filter { 
        it.name.contains(query, ignoreCase = true) || 
        it.description.contains(query, ignoreCase = true)
    }
}
