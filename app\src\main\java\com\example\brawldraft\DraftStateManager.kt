package com.example.brawldraft

import android.util.Log
import com.example.brawldraft.data.*
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow

class DraftStateManager {

    companion object {
        private const val TAG = "DraftStateManager"
    }

    private val _draftState = MutableStateFlow(DraftState())
    val draftState: StateFlow<DraftState> = _draftState.asStateFlow()

    private val _recommendations = MutableStateFlow<List<BrawlerRecommendation>>(emptyList())
    val recommendations: StateFlow<List<BrawlerRecommendation>> = _recommendations.asStateFlow()

    private val recommendationEngine = RecommendationEngine()
    
    fun getCurrentState(): DraftState = _draftState.value
    
    fun updateDraftState(update: DraftStateUpdate) {
        val currentState = _draftState.value
        val newState = when (update) {
            is DraftStateUpdate.PhaseChanged -> {
                Log.d(TAG, "Phase changed to: ${update.newPhase}")
                currentState.copy(
                    currentPhase = update.newPhase,
                    lastUpdated = System.currentTimeMillis()
                )
            }
            
            is DraftStateUpdate.MapDetected -> {
                Log.d(TAG, "Map detected: ${update.map.name}")
                currentState.copy(
                    currentMap = update.map,
                    lastUpdated = System.currentTimeMillis()
                )
            }
            
            is DraftStateUpdate.BrawlerBanned -> {
                Log.d(TAG, "Brawler banned: ${update.brawler.name} by ${update.team}")
                val action = DraftAction(update.brawler, update.team, ActionType.BAN, currentState.currentPhase)
                
                val updatedBannedList = currentState.bannedBrawlers + update.brawler
                val updatedHistory = currentState.draftHistory + action
                
                val updatedTeamState = when (update.team) {
                    TeamSide.BLUE -> currentState.blueTeam.copy(
                        bannedBrawlers = currentState.blueTeam.bannedBrawlers + update.brawler
                    )
                    TeamSide.RED -> currentState.redTeam.copy(
                        bannedBrawlers = currentState.redTeam.bannedBrawlers + update.brawler
                    )
                }
                
                val newState = when (update.team) {
                    TeamSide.BLUE -> currentState.copy(
                        blueTeam = updatedTeamState,
                        bannedBrawlers = updatedBannedList,
                        draftHistory = updatedHistory,
                        lastUpdated = System.currentTimeMillis()
                    )
                    TeamSide.RED -> currentState.copy(
                        redTeam = updatedTeamState,
                        bannedBrawlers = updatedBannedList,
                        draftHistory = updatedHistory,
                        lastUpdated = System.currentTimeMillis()
                    )
                }
                
                // Auto-advance phase if ban phase is complete
                if (newState.currentPhase == DraftPhase.BAN_PHASE && newState.getTotalBans() >= 6) {
                    newState.copy(currentPhase = DraftPhase.PICK_PHASE_1)
                } else {
                    newState
                }
            }
            
            is DraftStateUpdate.BrawlerPicked -> {
                Log.d(TAG, "Brawler picked: ${update.brawler.name} by ${update.team}")
                val action = DraftAction(update.brawler, update.team, ActionType.PICK, currentState.currentPhase)
                
                val updatedHistory = currentState.draftHistory + action
                
                val updatedTeamState = when (update.team) {
                    TeamSide.BLUE -> currentState.blueTeam.copy(
                        pickedBrawlers = currentState.blueTeam.pickedBrawlers + update.brawler
                    )
                    TeamSide.RED -> currentState.redTeam.copy(
                        pickedBrawlers = currentState.redTeam.pickedBrawlers + update.brawler
                    )
                }
                
                val newState = when (update.team) {
                    TeamSide.BLUE -> currentState.copy(
                        blueTeam = updatedTeamState,
                        draftHistory = updatedHistory,
                        lastUpdated = System.currentTimeMillis()
                    )
                    TeamSide.RED -> currentState.copy(
                        redTeam = updatedTeamState,
                        draftHistory = updatedHistory,
                        lastUpdated = System.currentTimeMillis()
                    )
                }
                
                // Auto-advance phases based on pick counts
                advancePickPhase(newState)
            }
            
            is DraftStateUpdate.DraftReset -> {
                Log.d(TAG, "Draft reset")
                DraftState()
            }
            
            is DraftStateUpdate.ManualCorrection -> {
                Log.d(TAG, "Manual correction: ${update.action}")
                // TODO: Implement manual correction logic
                currentState
            }
        }
        
        _draftState.value = newState
        updateRecommendations(newState)
    }
    
    private fun advancePickPhase(state: DraftState): DraftState {
        return when (state.currentPhase) {
            DraftPhase.PICK_PHASE_1 -> {
                if (state.blueTeam.pickedBrawlers.size >= 1) {
                    state.copy(currentPhase = DraftPhase.PICK_PHASE_2)
                } else state
            }
            DraftPhase.PICK_PHASE_2 -> {
                if (state.redTeam.pickedBrawlers.size >= 2) {
                    state.copy(currentPhase = DraftPhase.PICK_PHASE_3)
                } else state
            }
            DraftPhase.PICK_PHASE_3 -> {
                if (state.blueTeam.pickedBrawlers.size >= 3) {
                    state.copy(currentPhase = DraftPhase.PICK_PHASE_4)
                } else state
            }
            DraftPhase.PICK_PHASE_4 -> {
                if (state.redTeam.pickedBrawlers.size >= 3) {
                    state.copy(currentPhase = DraftPhase.COMPLETED)
                } else state
            }
            else -> state
        }
    }
    
    private fun updateRecommendations(state: DraftState) {
        // Generate recommendations based on current state
        val newRecommendations = generateRecommendations(state)
        _recommendations.value = newRecommendations
    }
    
    private fun generateRecommendations(state: DraftState): List<BrawlerRecommendation> {
        val expectedAction = state.getNextExpectedAction()

        return if (expectedAction != null) {
            // Generate recommendations based on the expected action type
            recommendationEngine.generateRecommendations(state, expectedAction.actionType)
        } else {
            // No specific action expected, provide general recommendations
            val availableBrawlers = BrawlerDatabase.getAllBrawlers().filter { brawler ->
                state.isBrawlerAvailable(brawler)
            }

            availableBrawlers.take(5).map { brawler ->
                BrawlerRecommendation(
                    brawler = brawler,
                    score = brawler.winRate,
                    reason = "High win rate (${(brawler.winRate * 100).toInt()}%)",
                    priority = when {
                        brawler.winRate > 0.6f -> RecommendationPriority.HIGH
                        brawler.winRate > 0.5f -> RecommendationPriority.MEDIUM
                        else -> RecommendationPriority.LOW
                    }
                )
            }.sortedByDescending { it.score }
        }
    }
    
    fun startNewDraft() {
        updateDraftState(DraftStateUpdate.DraftReset())
    }
    
    fun detectMapFromScreen(mapName: String) {
        // TODO: Map detection from screen analysis
        val detectedMap = GameMap(
            id = mapName.lowercase().replace(" ", "_"),
            name = mapName,
            mode = GameMode.UNKNOWN // Will be determined by map name
        )
        updateDraftState(DraftStateUpdate.MapDetected(detectedMap))
    }
    
    fun manualBrawlerAction(brawler: Brawler, team: TeamSide, actionType: ActionType) {
        when (actionType) {
            ActionType.BAN -> updateDraftState(DraftStateUpdate.BrawlerBanned(brawler, team))
            ActionType.PICK -> updateDraftState(DraftStateUpdate.BrawlerPicked(brawler, team))
        }
    }
    
    fun undoLastAction() {
        val currentState = _draftState.value
        if (currentState.draftHistory.isNotEmpty()) {
            // TODO: Implement undo functionality
            Log.d(TAG, "Undo requested - not yet implemented")
        }
    }

    fun getRecommendationsForAction(actionType: ActionType): List<BrawlerRecommendation> {
        val currentState = _draftState.value
        return recommendationEngine.generateRecommendations(currentState, actionType)
    }

    fun getCurrentRecommendations(): List<BrawlerRecommendation> {
        return _recommendations.value
    }
}

data class BrawlerRecommendation(
    val brawler: Brawler,
    val score: Float,
    val reason: String,
    val priority: RecommendationPriority = RecommendationPriority.MEDIUM
)

enum class RecommendationPriority {
    LOW,
    MEDIUM,
    HIGH,
    CRITICAL
}
