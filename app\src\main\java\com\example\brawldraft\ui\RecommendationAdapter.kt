package com.example.brawldraft.ui

import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.TextView
import androidx.core.content.ContextCompat
import androidx.recyclerview.widget.DiffUtil
import androidx.recyclerview.widget.ListAdapter
import androidx.recyclerview.widget.RecyclerView
import com.example.brawldraft.BrawlerRecommendation
import com.example.brawldraft.R
import com.example.brawldraft.RecommendationPriority

class RecommendationAdapter(
    private val onRecommendationClick: (BrawlerRecommendation) -> Unit
) : ListAdapter<BrawlerRecommendation, RecommendationAdapter.RecommendationViewHolder>(DiffCallback()) {

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): RecommendationViewHolder {
        val view = LayoutInflater.from(parent.context)
            .inflate(R.layout.item_recommendation, parent, false)
        return RecommendationViewHolder(view)
    }

    override fun onBindViewHolder(holder: RecommendationViewHolder, position: Int) {
        holder.bind(getItem(position))
    }

    inner class RecommendationViewHolder(itemView: View) : RecyclerView.ViewHolder(itemView) {
        private val priorityIndicator: View = itemView.findViewById(R.id.priority_indicator)
        private val brawlerName: TextView = itemView.findViewById(R.id.brawler_name)
        private val scoreText: TextView = itemView.findViewById(R.id.score_text)
        private val reasonText: TextView = itemView.findViewById(R.id.reason_text)

        fun bind(recommendation: BrawlerRecommendation) {
            brawlerName.text = recommendation.brawler.name
            scoreText.text = "${(recommendation.score * 100).toInt()}%"
            reasonText.text = recommendation.reason

            // Set priority indicator color
            val priorityColor = when (recommendation.priority) {
                RecommendationPriority.CRITICAL -> ContextCompat.getColor(itemView.context, R.color.gaming_danger)
                RecommendationPriority.HIGH -> ContextCompat.getColor(itemView.context, R.color.gaming_warning)
                RecommendationPriority.MEDIUM -> ContextCompat.getColor(itemView.context, R.color.gaming_accent)
                RecommendationPriority.LOW -> ContextCompat.getColor(itemView.context, R.color.gaming_text_secondary)
            }
            priorityIndicator.setBackgroundColor(priorityColor)

            itemView.setOnClickListener {
                onRecommendationClick(recommendation)
            }
        }
    }

    private class DiffCallback : DiffUtil.ItemCallback<BrawlerRecommendation>() {
        override fun areItemsTheSame(
            oldItem: BrawlerRecommendation,
            newItem: BrawlerRecommendation
        ): Boolean {
            return oldItem.brawler.id == newItem.brawler.id
        }

        override fun areContentsTheSame(
            oldItem: BrawlerRecommendation,
            newItem: BrawlerRecommendation
        ): Boolean {
            return oldItem == newItem
        }
    }
}
