{"logs": [{"outputFile": "com.example.brawldraft.app-mergeDebugResources-30:/values-hy/values-hy.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\2aa33be5c222f8571691eafceec0feae\\transformed\\material-1.10.0\\res\\values-hy\\values-hy.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,265,341,417,497,589,677,772,902,983,1047,1144,1229,1291,1378,1440,1504,1565,1632,1693,1747,1869,1926,1986,2040,2121,2256,2340,2425,2561,2636,2711,2854,2949,3029,3085,3138,3204,3278,3357,3443,3526,3597,3673,3749,3826,3932,4020,4100,4196,4292,4366,4444,4544,4595,4679,4748,4835,4926,4988,5052,5115,5186,5291,5397,5497,5600,5660,5717", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73", "endColumns": "12,75,75,79,91,87,94,129,80,63,96,84,61,86,61,63,60,66,60,53,121,56,59,53,80,134,83,84,135,74,74,142,94,79,55,52,65,73,78,85,82,70,75,75,76,105,87,79,95,95,73,77,99,50,83,68,86,90,61,63,62,70,104,105,99,102,59,56,84", "endOffsets": "260,336,412,492,584,672,767,897,978,1042,1139,1224,1286,1373,1435,1499,1560,1627,1688,1742,1864,1921,1981,2035,2116,2251,2335,2420,2556,2631,2706,2849,2944,3024,3080,3133,3199,3273,3352,3438,3521,3592,3668,3744,3821,3927,4015,4095,4191,4287,4361,4439,4539,4590,4674,4743,4830,4921,4983,5047,5110,5181,5286,5392,5492,5595,5655,5712,5797"}, "to": {"startLines": "2,33,34,35,36,37,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,3024,3100,3176,3256,3348,4156,4251,4381,4462,4526,4623,4708,4770,4857,4919,4983,5044,5111,5172,5226,5348,5405,5465,5519,5600,5735,5819,5904,6040,6115,6190,6333,6428,6508,6564,6617,6683,6757,6836,6922,7005,7076,7152,7228,7305,7411,7499,7579,7675,7771,7845,7923,8023,8074,8158,8227,8314,8405,8467,8531,8594,8665,8770,8876,8976,9079,9139,9196", "endLines": "5,33,34,35,36,37,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107", "endColumns": "12,75,75,79,91,87,94,129,80,63,96,84,61,86,61,63,60,66,60,53,121,56,59,53,80,134,83,84,135,74,74,142,94,79,55,52,65,73,78,85,82,70,75,75,76,105,87,79,95,95,73,77,99,50,83,68,86,90,61,63,62,70,104,105,99,102,59,56,84", "endOffsets": "310,3095,3171,3251,3343,3431,4246,4376,4457,4521,4618,4703,4765,4852,4914,4978,5039,5106,5167,5221,5343,5400,5460,5514,5595,5730,5814,5899,6035,6110,6185,6328,6423,6503,6559,6612,6678,6752,6831,6917,7000,7071,7147,7223,7300,7406,7494,7574,7670,7766,7840,7918,8018,8069,8153,8222,8309,8400,8462,8526,8589,8660,8765,8871,8971,9074,9134,9191,9276"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\57562ad43921b16639a6438f83b538f4\\transformed\\appcompat-1.6.1\\res\\values-hy\\values-hy.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,213,313,423,512,618,735,817,897,988,1081,1176,1270,1370,1463,1558,1652,1743,1834,1917,2023,2129,2228,2338,2446,2547,2717,2814", "endColumns": "107,99,109,88,105,116,81,79,90,92,94,93,99,92,94,93,90,90,82,105,105,98,109,107,100,169,96,82", "endOffsets": "208,308,418,507,613,730,812,892,983,1076,1171,1265,1365,1458,1553,1647,1738,1829,1912,2018,2124,2223,2333,2441,2542,2712,2809,2892"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,108", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "315,423,523,633,722,828,945,1027,1107,1198,1291,1386,1480,1580,1673,1768,1862,1953,2044,2127,2233,2339,2438,2548,2656,2757,2927,9281", "endColumns": "107,99,109,88,105,116,81,79,90,92,94,93,99,92,94,93,90,90,82,105,105,98,109,107,100,169,96,82", "endOffsets": "418,518,628,717,823,940,1022,1102,1193,1286,1381,1475,1575,1668,1763,1857,1948,2039,2122,2228,2334,2433,2543,2651,2752,2922,3019,9359"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\27d9032238570e73400cc8465b116304\\transformed\\core-1.10.1\\res\\values-hy\\values-hy.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,155,260,358,457,562,664,775", "endColumns": "99,104,97,98,104,101,110,100", "endOffsets": "150,255,353,452,557,659,770,871"}, "to": {"startLines": "38,39,40,41,42,43,44,109", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3436,3536,3641,3739,3838,3943,4045,9364", "endColumns": "99,104,97,98,104,101,110,100", "endOffsets": "3531,3636,3734,3833,3938,4040,4151,9460"}}]}]}