package com.example.brawldraft.vision

import android.content.Context
import android.graphics.Bitmap
import android.graphics.BitmapFactory
import android.util.Log
import com.example.brawldraft.assets.AssetManager
import com.example.brawldraft.data.Brawler
import com.example.brawldraft.data.BrawlerDatabase
import kotlinx.coroutines.runBlocking
import org.opencv.android.Utils
import org.opencv.core.*
import org.opencv.imgproc.Imgproc
import java.io.IOException

class TemplateMatcher {
    
    companion object {
        private const val TAG = "TemplateMatcher"
        private const val TEMPLATE_MATCH_THRESHOLD = 0.75
    }
    
    private val brawlerTemplates = mutableMapOf<String, Mat>()
    private var isInitialized = false
    private lateinit var assetManager: AssetManager
    
    fun initialize(context: Context) {
        if (isInitialized) return
        
        try {
            loadBrawlerTemplates(context)
            isInitialized = true
            Log.d(TAG, "Template matcher initialized with ${brawlerTemplates.size} templates")
        } catch (e: Exception) {
            Log.e(TAG, "Failed to initialize template matcher", e)
        }
    }
    
    private fun loadBrawlerTemplates(context: Context) {
        // Load template images for each brawler
        // For now, we'll create placeholder templates
        // In a real implementation, you would load actual brawler icon images
        
        val allBrawlers = BrawlerDatabase.getAllBrawlers()
        
        for (brawler in allBrawlers) {
            try {
                // Try to load template from assets
                val template = loadTemplateFromAssets(context, brawler.id)
                if (template != null) {
                    brawlerTemplates[brawler.id] = template
                } else {
                    // Create a placeholder template if no asset found
                    val placeholderTemplate = createPlaceholderTemplate()
                    brawlerTemplates[brawler.id] = placeholderTemplate
                }
            } catch (e: Exception) {
                Log.w(TAG, "Failed to load template for ${brawler.name}", e)
            }
        }
    }
    
    private fun loadTemplateFromAssets(context: Context, brawlerId: String): Mat? {
        return try {
            // Try to load from assets/brawler_icons/
            val inputStream = context.assets.open("brawler_icons/${brawlerId}.png")
            val bitmap = BitmapFactory.decodeStream(inputStream)
            inputStream.close()
            
            if (bitmap != null) {
                val mat = Mat()
                Utils.bitmapToMat(bitmap, mat)
                
                // Convert to RGB
                val rgbMat = Mat()
                Imgproc.cvtColor(mat, rgbMat, Imgproc.COLOR_RGBA2RGB)
                mat.release()
                
                rgbMat
            } else {
                null
            }
        } catch (e: IOException) {
            // Asset not found, return null
            null
        } catch (e: Exception) {
            Log.e(TAG, "Error loading template for $brawlerId", e)
            null
        }
    }
    
    private fun createPlaceholderTemplate(): Mat {
        // Create a simple placeholder template (solid color square)
        val template = Mat(64, 64, CvType.CV_8UC3, Scalar(128.0, 128.0, 128.0))
        return template
    }
    
    fun matchBrawlerTemplate(roi: Mat): TemplateMatchResult? {
        if (!isInitialized) {
            Log.w(TAG, "Template matcher not initialized")
            return null
        }
        
        var bestMatch: TemplateMatchResult? = null
        var bestScore = 0.0
        
        // Resize ROI to standard template size for better matching
        val resizedRoi = Mat()
        val templateSize = Size(64.0, 64.0)
        Imgproc.resize(roi, resizedRoi, templateSize)
        
        // Try matching against all brawler templates
        for ((brawlerId, template) in brawlerTemplates) {
            try {
                val matchScore = performTemplateMatch(resizedRoi, template)
                
                if (matchScore > bestScore && matchScore > TEMPLATE_MATCH_THRESHOLD) {
                    bestScore = matchScore
                    val brawler = BrawlerDatabase.getBrawlerById(brawlerId)
                    if (brawler != null) {
                        bestMatch = TemplateMatchResult(
                            brawler = brawler,
                            confidence = matchScore.toFloat(),
                            matchMethod = "template_matching"
                        )
                    }
                }
            } catch (e: Exception) {
                Log.e(TAG, "Error matching template for $brawlerId", e)
            }
        }
        
        resizedRoi.release()
        
        if (bestMatch != null) {
            Log.d(TAG, "Best match: ${bestMatch.brawler.name} with confidence ${bestMatch.confidence}")
        }
        
        return bestMatch
    }
    
    private fun performTemplateMatch(image: Mat, template: Mat): Double {
        return try {
            // Ensure both images are the same size
            if (image.size() != template.size()) {
                val resizedTemplate = Mat()
                Imgproc.resize(template, resizedTemplate, image.size())
                val result = performActualTemplateMatch(image, resizedTemplate)
                resizedTemplate.release()
                result
            } else {
                performActualTemplateMatch(image, template)
            }
        } catch (e: Exception) {
            Log.e(TAG, "Error in template matching", e)
            0.0
        }
    }
    
    private fun performActualTemplateMatch(image: Mat, template: Mat): Double {
        val result = Mat()
        
        // Use normalized correlation coefficient matching
        Imgproc.matchTemplate(image, template, result, Imgproc.TM_CCOEFF_NORMED)
        
        // Find the best match
        val minMaxLocResult = Core.minMaxLoc(result)
        val maxVal = minMaxLocResult.maxVal
        
        result.release()
        
        return maxVal
    }
    
    fun matchMultipleBrawlers(roi: Mat, maxResults: Int = 3): List<TemplateMatchResult> {
        if (!isInitialized) {
            Log.w(TAG, "Template matcher not initialized")
            return emptyList()
        }
        
        val results = mutableListOf<TemplateMatchResult>()
        
        // Resize ROI to standard template size
        val resizedRoi = Mat()
        val templateSize = Size(64.0, 64.0)
        Imgproc.resize(roi, resizedRoi, templateSize)
        
        // Try matching against all brawler templates
        for ((brawlerId, template) in brawlerTemplates) {
            try {
                val matchScore = performTemplateMatch(resizedRoi, template)
                
                if (matchScore > TEMPLATE_MATCH_THRESHOLD) {
                    val brawler = BrawlerDatabase.getBrawlerById(brawlerId)
                    if (brawler != null) {
                        results.add(
                            TemplateMatchResult(
                                brawler = brawler,
                                confidence = matchScore.toFloat(),
                                matchMethod = "template_matching"
                            )
                        )
                    }
                }
            } catch (e: Exception) {
                Log.e(TAG, "Error matching template for $brawlerId", e)
            }
        }
        
        resizedRoi.release()
        
        // Sort by confidence and return top results
        return results.sortedByDescending { it.confidence }.take(maxResults)
    }
    
    fun cleanup() {
        for (template in brawlerTemplates.values) {
            template.release()
        }
        brawlerTemplates.clear()
        isInitialized = false
        Log.d(TAG, "Template matcher cleaned up")
    }
}

data class TemplateMatchResult(
    val brawler: Brawler,
    val confidence: Float,
    val matchMethod: String
)
