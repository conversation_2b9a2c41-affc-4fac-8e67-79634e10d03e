package com.example.brawldraft.vision

import android.graphics.Bitmap
import android.util.Log
import com.example.brawldraft.data.*
import org.opencv.android.Utils
import org.opencv.core.*
import org.opencv.imgproc.Imgproc
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext

class ImageAnalyzer {
    
    companion object {
        private const val TAG = "ImageAnalyzer"
        private const val MATCH_THRESHOLD = 0.8 // Template matching threshold
    }
    
    private val templateMatcher = TemplateMatcher()
    
    suspend fun analyzeScreenCapture(bitmap: Bitmap): AnalysisResult = withContext(Dispatchers.Default) {
        try {
            Log.d(TAG, "Starting image analysis for ${bitmap.width}x${bitmap.height} bitmap")
            
            // Convert bitmap to OpenCV Mat
            val mat = Mat()
            Utils.bitmapToMat(bitmap, mat)
            
            // Convert to RGB (OpenCV uses BGR by default)
            val rgbMat = Mat()
            Imgproc.cvtColor(mat, rgbMat, Imgproc.COLOR_RGBA2RGB)
            
            // Analyze different regions of the screen
            val gameState = detectGameState(rgbMat)
            val detectedMap = detectMap(rgbMat)
            val detectedBrawlers = detectBrawlers(rgbMat)
            val draftPhase = detectDraftPhase(rgbMat)
            
            // Clean up
            mat.release()
            rgbMat.release()
            
            AnalysisResult(
                gameState = gameState,
                detectedMap = detectedMap,
                detectedBrawlers = detectedBrawlers,
                currentPhase = draftPhase,
                confidence = calculateOverallConfidence(gameState, detectedMap, detectedBrawlers),
                timestamp = System.currentTimeMillis()
            )
            
        } catch (e: Exception) {
            Log.e(TAG, "Error during image analysis", e)
            AnalysisResult.empty()
        }
    }
    
    private fun detectGameState(mat: Mat): GameState {
        // Look for UI elements that indicate we're in a draft
        // This is a simplified implementation - in reality, we'd look for specific UI patterns
        
        // Check for draft-specific UI elements
        val isDraftActive = checkForDraftUI(mat)
        val isBanPhase = checkForBanPhaseUI(mat)
        val isPickPhase = checkForPickPhaseUI(mat)
        
        return when {
            !isDraftActive -> GameState.MENU
            isBanPhase -> GameState.DRAFT_BAN_PHASE
            isPickPhase -> GameState.DRAFT_PICK_PHASE
            else -> GameState.UNKNOWN
        }
    }
    
    private fun detectMap(mat: Mat): GameMap? {
        // Look for map name text or map preview image
        // This would involve OCR or template matching against known map images
        
        // For now, return null - will be implemented with actual template matching
        return null
    }
    
    private fun detectBrawlers(mat: Mat): List<DetectedBrawler> {
        val detectedBrawlers = mutableListOf<DetectedBrawler>()
        
        // Define regions where brawler icons typically appear
        val brawlerRegions = getBrawlerIconRegions(mat)
        
        for (region in brawlerRegions) {
            val detectedBrawler = detectBrawlerInRegion(mat, region)
            if (detectedBrawler != null) {
                detectedBrawlers.add(detectedBrawler)
            }
        }
        
        return detectedBrawlers
    }
    
    private fun detectDraftPhase(mat: Mat): DraftPhase {
        // Analyze UI elements to determine current draft phase
        // Look for phase indicators, timers, or specific UI layouts
        
        return DraftPhase.WAITING // Placeholder
    }
    
    private fun checkForDraftUI(mat: Mat): Boolean {
        // Look for draft-specific UI elements
        // This could include checking for specific colors, layouts, or text
        
        // Simplified check - look for dark areas that might indicate draft overlay
        val grayMat = Mat()
        Imgproc.cvtColor(mat, grayMat, Imgproc.COLOR_RGB2GRAY)
        
        val mean = Core.mean(grayMat)
        grayMat.release()
        
        // If the image is generally dark, it might be in draft mode
        return mean.`val`[0] < 100.0
    }
    
    private fun checkForBanPhaseUI(mat: Mat): Boolean {
        // Look for ban phase specific UI elements
        // This could include red X marks, ban timers, etc.
        return false // Placeholder
    }
    
    private fun checkForPickPhaseUI(mat: Mat): Boolean {
        // Look for pick phase specific UI elements
        // This could include pick timers, highlighted brawlers, etc.
        return false // Placeholder
    }
    
    private fun getBrawlerIconRegions(mat: Mat): List<Rect> {
        // Define regions where brawler icons typically appear in the draft UI
        // These would be based on the actual Brawl Stars draft interface layout
        
        val regions = mutableListOf<Rect>()
        val width = mat.width()
        val height = mat.height()
        
        // Example regions - these would need to be calibrated for actual game UI
        // Ban area (top of screen)
        for (i in 0 until 6) {
            val x = (width * 0.1 + i * width * 0.13).toInt()
            val y = (height * 0.1).toInt()
            val w = (width * 0.08).toInt()
            val h = (height * 0.08).toInt()
            regions.add(Rect(x, y, w, h))
        }
        
        // Pick area (bottom of screen)
        for (i in 0 until 6) {
            val x = (width * 0.1 + i * width * 0.13).toInt()
            val y = (height * 0.7).toInt()
            val w = (width * 0.08).toInt()
            val h = (height * 0.08).toInt()
            regions.add(Rect(x, y, w, h))
        }
        
        return regions
    }
    
    private fun detectBrawlerInRegion(mat: Mat, region: Rect): DetectedBrawler? {
        try {
            // Extract the region of interest
            val roi = Mat(mat, region)
            
            // Use template matching to identify the brawler
            val matchResult = templateMatcher.matchBrawlerTemplate(roi)
            
            roi.release()
            
            if (matchResult != null && matchResult.confidence > MATCH_THRESHOLD) {
                return DetectedBrawler(
                    brawler = matchResult.brawler,
                    confidence = matchResult.confidence,
                    region = region,
                    state = BrawlerState.UNKNOWN
                )
            }
            
        } catch (e: Exception) {
            Log.e(TAG, "Error detecting brawler in region", e)
        }
        
        return null
    }
    
    private fun calculateOverallConfidence(
        gameState: GameState,
        detectedMap: GameMap?,
        detectedBrawlers: List<DetectedBrawler>
    ): Float {
        var confidence = 0.0f
        var factors = 0
        
        // Game state confidence
        if (gameState != GameState.UNKNOWN) {
            confidence += 0.3f
            factors++
        }
        
        // Map detection confidence
        if (detectedMap != null) {
            confidence += 0.2f
            factors++
        }
        
        // Brawler detection confidence
        if (detectedBrawlers.isNotEmpty()) {
            val avgBrawlerConfidence = detectedBrawlers.map { it.confidence }.average().toFloat()
            confidence += avgBrawlerConfidence * 0.5f
            factors++
        }
        
        return if (factors > 0) confidence / factors else 0.0f
    }
}

data class AnalysisResult(
    val gameState: GameState,
    val detectedMap: GameMap?,
    val detectedBrawlers: List<DetectedBrawler>,
    val currentPhase: DraftPhase,
    val confidence: Float,
    val timestamp: Long
) {
    companion object {
        fun empty() = AnalysisResult(
            gameState = GameState.UNKNOWN,
            detectedMap = null,
            detectedBrawlers = emptyList(),
            currentPhase = DraftPhase.WAITING,
            confidence = 0.0f,
            timestamp = System.currentTimeMillis()
        )
    }
}

data class DetectedBrawler(
    val brawler: Brawler,
    val confidence: Float,
    val region: Rect,
    val state: BrawlerState
)

enum class GameState {
    MENU,
    DRAFT_BAN_PHASE,
    DRAFT_PICK_PHASE,
    IN_GAME,
    UNKNOWN
}

enum class BrawlerState {
    AVAILABLE,
    BANNED,
    PICKED_BLUE,
    PICKED_RED,
    UNKNOWN
}
