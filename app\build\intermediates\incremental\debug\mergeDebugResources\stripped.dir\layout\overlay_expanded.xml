<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="360dp"
    android:layout_height="wrap_content"
    android:background="@drawable/bg_overlay_expanded"
    android:orientation="vertical"
    android:padding="16dp">

    <!-- Header -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:gravity="center_vertical"
        android:layout_marginBottom="12dp">

        <TextView
            android:id="@+id/overlay_title"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:text="Brawl Draft Helper"
            android:textColor="@color/gaming_text_primary"
            android:textSize="16sp"
            android:textStyle="bold" />

        <ImageButton
            android:id="@+id/minimize_button"
            android:layout_width="32dp"
            android:layout_height="32dp"
            android:layout_marginEnd="8dp"
            android:background="?android:attr/selectableItemBackgroundBorderless"
            android:src="@android:drawable/ic_menu_close_clear_cancel"
            android:tint="@color/gaming_text_secondary" />

        <ImageButton
            android:id="@+id/close_button"
            android:layout_width="32dp"
            android:layout_height="32dp"
            android:background="?android:attr/selectableItemBackgroundBorderless"
            android:src="@android:drawable/ic_menu_close_clear_cancel"
            android:tint="@color/gaming_danger" />

    </LinearLayout>

    <!-- Draft Status -->
    <TextView
        android:id="@+id/draft_status"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="Waiting for draft to start..."
        android:textColor="@color/gaming_text_secondary"
        android:textSize="14sp"
        android:gravity="center"
        android:layout_marginBottom="12dp" />

    <!-- Quick Actions -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:gravity="center"
        android:layout_marginBottom="12dp">

        <Button
            android:id="@+id/start_capture_button"
            android:layout_width="0dp"
            android:layout_height="36dp"
            android:layout_weight="1"
            android:layout_marginEnd="8dp"
            android:text="Start Capture"
            android:textSize="12sp"
            android:backgroundTint="@color/gaming_success" />

        <Button
            android:id="@+id/manual_mode_button"
            android:layout_width="0dp"
            android:layout_height="36dp"
            android:layout_weight="1"
            android:text="Manual Mode"
            android:textSize="12sp"
            android:backgroundTint="@color/gaming_accent" />

    </LinearLayout>

    <!-- Draft Teams Display -->
    <LinearLayout
        android:id="@+id/draft_teams_container"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:layout_marginBottom="12dp"
        android:visibility="gone">

        <!-- Blue Team -->
        <LinearLayout
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:orientation="vertical"
            android:layout_marginEnd="4dp">

            <TextView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="Your Team"
                android:textColor="@color/gaming_accent"
                android:textSize="12sp"
                android:textStyle="bold"
                android:gravity="center"
                android:layout_marginBottom="4dp" />

            <LinearLayout
                android:id="@+id/blue_team_brawlers"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical" />

        </LinearLayout>

        <!-- Red Team -->
        <LinearLayout
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:orientation="vertical"
            android:layout_marginStart="4dp">

            <TextView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="Enemy Team"
                android:textColor="@color/gaming_danger"
                android:textSize="12sp"
                android:textStyle="bold"
                android:gravity="center"
                android:layout_marginBottom="4dp" />

            <LinearLayout
                android:id="@+id/red_team_brawlers"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical" />

        </LinearLayout>

    </LinearLayout>

    <!-- Recommendations Section -->
    <LinearLayout
        android:id="@+id/recommendations_container"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:layout_marginBottom="12dp"
        android:visibility="gone">

        <TextView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="Recommendations"
            android:textColor="@color/gaming_text_primary"
            android:textSize="14sp"
            android:textStyle="bold"
            android:layout_marginBottom="8dp" />

        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/recommendations_recycler"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="8dp" />

    </LinearLayout>

    <!-- Manual Override Section -->
    <LinearLayout
        android:id="@+id/manual_override_container"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:visibility="gone">

        <TextView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="Manual Override"
            android:textColor="@color/gaming_text_primary"
            android:textSize="14sp"
            android:textStyle="bold"
            android:layout_marginBottom="8dp" />

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:gravity="center">

            <Button
                android:id="@+id/manual_ban_button"
                android:layout_width="0dp"
                android:layout_height="32dp"
                android:layout_weight="1"
                android:layout_marginEnd="4dp"
                android:text="Ban"
                android:textSize="11sp"
                android:backgroundTint="@color/gaming_danger" />

            <Button
                android:id="@+id/manual_pick_button"
                android:layout_width="0dp"
                android:layout_height="32dp"
                android:layout_weight="1"
                android:layout_marginStart="4dp"
                android:text="Pick"
                android:textSize="11sp"
                android:backgroundTint="@color/gaming_success" />

        </LinearLayout>

    </LinearLayout>

</LinearLayout>