<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="320dp"
    android:layout_height="wrap_content"
    android:background="@drawable/bg_overlay_expanded"
    android:orientation="vertical"
    android:padding="16dp">

    <!-- Header -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:gravity="center_vertical"
        android:layout_marginBottom="12dp">

        <TextView
            android:id="@+id/overlay_title"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:text="Brawl Draft Helper"
            android:textColor="@color/gaming_text_primary"
            android:textSize="16sp"
            android:textStyle="bold" />

        <ImageButton
            android:id="@+id/minimize_button"
            android:layout_width="32dp"
            android:layout_height="32dp"
            android:layout_marginEnd="8dp"
            android:background="?android:attr/selectableItemBackgroundBorderless"
            android:src="@android:drawable/ic_menu_close_clear_cancel"
            android:tint="@color/gaming_text_secondary" />

        <ImageButton
            android:id="@+id/close_button"
            android:layout_width="32dp"
            android:layout_height="32dp"
            android:background="?android:attr/selectableItemBackgroundBorderless"
            android:src="@android:drawable/ic_menu_close_clear_cancel"
            android:tint="@color/gaming_danger" />

    </LinearLayout>

    <!-- Draft Status -->
    <TextView
        android:id="@+id/draft_status"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="Waiting for draft to start..."
        android:textColor="@color/gaming_text_secondary"
        android:textSize="14sp"
        android:gravity="center"
        android:layout_marginBottom="12dp" />

    <!-- Quick Actions -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:gravity="center">

        <Button
            android:id="@+id/start_capture_button"
            android:layout_width="0dp"
            android:layout_height="36dp"
            android:layout_weight="1"
            android:layout_marginEnd="8dp"
            android:text="Start Capture"
            android:textSize="12sp"
            android:backgroundTint="@color/gaming_success" />

        <Button
            android:id="@+id/manual_mode_button"
            android:layout_width="0dp"
            android:layout_height="36dp"
            android:layout_weight="1"
            android:text="Manual Mode"
            android:textSize="12sp"
            android:backgroundTint="@color/gaming_accent" />

    </LinearLayout>

</LinearLayout>