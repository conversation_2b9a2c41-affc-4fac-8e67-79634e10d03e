package com.example.brawldraft

import android.util.Log
import com.example.brawldraft.data.*
import kotlin.math.max
import kotlin.math.min

class RecommendationEngine {
    
    companion object {
        private const val TAG = "RecommendationEngine"
        private const val MAX_RECOMMENDATIONS = 5
    }
    
    fun generateRecommendations(
        draftState: DraftState,
        actionType: ActionType
    ): List<BrawlerRecommendation> {
        
        Log.d(TAG, "Generating ${actionType.name} recommendations for phase ${draftState.currentPhase}")
        
        return when (actionType) {
            ActionType.BAN -> generateBanRecommendations(draftState)
            ActionType.PICK -> generatePickRecommendations(draftState)
        }
    }
    
    private fun generateBanRecommendations(draftState: DraftState): List<BrawlerRecommendation> {
        val availableBrawlers = getAvailableBrawlers(draftState)
        val recommendations = mutableListOf<BrawlerRecommendation>()
        
        for (brawler in availableBrawlers) {
            val score = calculateBanScore(brawler, draftState)
            val reason = generateBanReason(brawler, draftState)
            val priority = determinePriority(score)
            
            recommendations.add(
                BrawlerRecommendation(
                    brawler = brawler,
                    score = score,
                    reason = reason,
                    priority = priority
                )
            )
        }
        
        return recommendations
            .sortedByDescending { it.score }
            .take(MAX_RECOMMENDATIONS)
    }
    
    private fun generatePickRecommendations(draftState: DraftState): List<BrawlerRecommendation> {
        val availableBrawlers = getAvailableBrawlers(draftState)
        val recommendations = mutableListOf<BrawlerRecommendation>()
        
        for (brawler in availableBrawlers) {
            val score = calculatePickScore(brawler, draftState)
            val reason = generatePickReason(brawler, draftState)
            val priority = determinePriority(score)
            
            recommendations.add(
                BrawlerRecommendation(
                    brawler = brawler,
                    score = score,
                    reason = reason,
                    priority = priority
                )
            )
        }
        
        return recommendations
            .sortedByDescending { it.score }
            .take(MAX_RECOMMENDATIONS)
    }
    
    private fun calculateBanScore(brawler: Brawler, draftState: DraftState): Float {
        var score = 0.0f
        
        // Base score from win rate and pick rate
        score += brawler.winRate * 0.4f
        score += brawler.pickRate * 0.3f
        
        // Map synergy bonus
        score += calculateMapSynergy(brawler, draftState.currentMap) * 0.2f
        
        // Counter potential (how much this brawler counters enemy picks)
        score += calculateCounterPotential(brawler, draftState.getEnemyTeam()) * 0.1f
        
        return min(score, 1.0f)
    }
    
    private fun calculatePickScore(brawler: Brawler, draftState: DraftState): Float {
        var score = 0.0f
        
        // Base score from win rate
        score += brawler.winRate * 0.3f
        
        // Map synergy (very important for picks)
        score += calculateMapSynergy(brawler, draftState.currentMap) * 0.3f
        
        // Team composition synergy
        score += calculateTeamSynergy(brawler, draftState.getPlayerTeam()) * 0.2f
        
        // Counter value against enemy team
        score += calculateCounterValue(brawler, draftState.getEnemyTeam()) * 0.2f
        
        return min(score, 1.0f)
    }
    
    private fun calculateMapSynergy(brawler: Brawler, map: GameMap): Float {
        // Map-specific brawler effectiveness
        return when (map.mode) {
            GameMode.GEM_GRAB -> calculateGemGrabSynergy(brawler)
            GameMode.BRAWL_BALL -> calculateBrawlBallSynergy(brawler)
            GameMode.BOUNTY -> calculateBountySynergy(brawler)
            GameMode.HEIST -> calculateHeistSynergy(brawler)
            GameMode.HOT_ZONE -> calculateHotZoneSynergy(brawler)
            GameMode.KNOCKOUT -> calculateKnockoutSynergy(brawler)
            GameMode.SHOWDOWN -> calculateShowdownSynergy(brawler)
            GameMode.UNKNOWN -> 0.5f // Neutral score for unknown maps
        }
    }
    
    private fun calculateGemGrabSynergy(brawler: Brawler): Float {
        return when (brawler.type) {
            BrawlerType.SUPPORT -> 0.8f // Supports are great for gem grab
            BrawlerType.CONTROLLER -> 0.7f // Controllers help control the center
            BrawlerType.TANK -> 0.6f // Tanks can hold gems
            BrawlerType.DAMAGE_DEALER -> 0.5f
            BrawlerType.MARKSMAN -> 0.4f
            BrawlerType.ARTILLERY -> 0.6f // Good for area control
            BrawlerType.ASSASSIN -> 0.3f // Less effective in gem grab
        }
    }
    
    private fun calculateBrawlBallSynergy(brawler: Brawler): Float {
        return when (brawler.type) {
            BrawlerType.TANK -> 0.7f // Good for ball carrying
            BrawlerType.DAMAGE_DEALER -> 0.6f
            BrawlerType.ASSASSIN -> 0.5f
            BrawlerType.SUPPORT -> 0.4f
            BrawlerType.CONTROLLER -> 0.6f
            BrawlerType.MARKSMAN -> 0.3f // Less effective in close quarters
            BrawlerType.ARTILLERY -> 0.4f
        }
    }
    
    private fun calculateBountySynergy(brawler: Brawler): Float {
        return when (brawler.type) {
            BrawlerType.MARKSMAN -> 0.8f // Excellent for bounty
            BrawlerType.DAMAGE_DEALER -> 0.7f
            BrawlerType.ARTILLERY -> 0.6f
            BrawlerType.ASSASSIN -> 0.5f
            BrawlerType.CONTROLLER -> 0.4f
            BrawlerType.SUPPORT -> 0.3f
            BrawlerType.TANK -> 0.2f // Least effective in bounty
        }
    }
    
    private fun calculateHeistSynergy(brawler: Brawler): Float {
        return when (brawler.type) {
            BrawlerType.DAMAGE_DEALER -> 0.8f // High DPS for safe destruction
            BrawlerType.ARTILLERY -> 0.7f // Good for safe damage
            BrawlerType.TANK -> 0.6f // Good for defense
            BrawlerType.MARKSMAN -> 0.5f
            BrawlerType.SUPPORT -> 0.4f
            BrawlerType.CONTROLLER -> 0.5f
            BrawlerType.ASSASSIN -> 0.3f
        }
    }
    
    private fun calculateHotZoneSynergy(brawler: Brawler): Float {
        return when (brawler.type) {
            BrawlerType.TANK -> 0.8f // Excellent for zone control
            BrawlerType.CONTROLLER -> 0.7f
            BrawlerType.SUPPORT -> 0.6f
            BrawlerType.DAMAGE_DEALER -> 0.5f
            BrawlerType.ARTILLERY -> 0.6f
            BrawlerType.MARKSMAN -> 0.4f
            BrawlerType.ASSASSIN -> 0.3f
        }
    }
    
    private fun calculateKnockoutSynergy(brawler: Brawler): Float {
        return when (brawler.type) {
            BrawlerType.ASSASSIN -> 0.7f // Good for eliminations
            BrawlerType.DAMAGE_DEALER -> 0.6f
            BrawlerType.MARKSMAN -> 0.6f
            BrawlerType.TANK -> 0.5f
            BrawlerType.CONTROLLER -> 0.5f
            BrawlerType.ARTILLERY -> 0.4f
            BrawlerType.SUPPORT -> 0.4f
        }
    }
    
    private fun calculateShowdownSynergy(brawler: Brawler): Float {
        return when (brawler.type) {
            BrawlerType.TANK -> 0.7f // Good survivability
            BrawlerType.ASSASSIN -> 0.6f
            BrawlerType.DAMAGE_DEALER -> 0.6f
            BrawlerType.MARKSMAN -> 0.5f
            BrawlerType.CONTROLLER -> 0.4f
            BrawlerType.ARTILLERY -> 0.3f
            BrawlerType.SUPPORT -> 0.2f // Least effective in solo
        }
    }
    
    private fun calculateTeamSynergy(brawler: Brawler, team: TeamState): Float {
        if (team.pickedBrawlers.isEmpty()) return 0.5f
        
        val teamTypes = team.pickedBrawlers.map { it.type }
        var synergy = 0.5f
        
        // Check for balanced composition
        val hasSupport = teamTypes.contains(BrawlerType.SUPPORT)
        val hasTank = teamTypes.contains(BrawlerType.TANK)
        val hasDamage = teamTypes.contains(BrawlerType.DAMAGE_DEALER) || 
                       teamTypes.contains(BrawlerType.MARKSMAN)
        
        when (brawler.type) {
            BrawlerType.SUPPORT -> {
                if (!hasSupport) synergy += 0.3f // Team needs support
                if (hasTank) synergy += 0.1f // Synergy with tanks
            }
            BrawlerType.TANK -> {
                if (!hasTank) synergy += 0.2f // Team needs tank
                if (hasSupport) synergy += 0.1f // Synergy with support
            }
            BrawlerType.DAMAGE_DEALER, BrawlerType.MARKSMAN -> {
                if (!hasDamage) synergy += 0.2f // Team needs damage
            }
            else -> {
                // Other types get neutral synergy
            }
        }
        
        return min(synergy, 1.0f)
    }
    
    private fun calculateCounterPotential(brawler: Brawler, enemyTeam: TeamState): Float {
        // How well this brawler counters enemy picks
        if (enemyTeam.pickedBrawlers.isEmpty()) return 0.3f
        
        var counterValue = 0.0f
        for (enemyBrawler in enemyTeam.pickedBrawlers) {
            counterValue += getCounterEffectiveness(brawler, enemyBrawler)
        }
        
        return min(counterValue / enemyTeam.pickedBrawlers.size, 1.0f)
    }
    
    private fun calculateCounterValue(brawler: Brawler, enemyTeam: TeamState): Float {
        return calculateCounterPotential(brawler, enemyTeam)
    }
    
    private fun getCounterEffectiveness(brawler: Brawler, enemy: Brawler): Float {
        // Simplified counter relationships
        return when {
            // Tanks counter assassins
            brawler.type == BrawlerType.TANK && enemy.type == BrawlerType.ASSASSIN -> 0.7f
            // Marksmen counter tanks at range
            brawler.type == BrawlerType.MARKSMAN && enemy.type == BrawlerType.TANK -> 0.6f
            // Assassins counter marksmen
            brawler.type == BrawlerType.ASSASSIN && enemy.type == BrawlerType.MARKSMAN -> 0.6f
            // Artillery counters grouped enemies
            brawler.type == BrawlerType.ARTILLERY && enemy.type == BrawlerType.SUPPORT -> 0.5f
            else -> 0.3f // Base counter value
        }
    }
    
    private fun getAvailableBrawlers(draftState: DraftState): List<Brawler> {
        return BrawlerDatabase.getAllBrawlers().filter { brawler ->
            draftState.isBrawlerAvailable(brawler)
        }
    }
    
    private fun generateBanReason(brawler: Brawler, draftState: DraftState): String {
        val reasons = mutableListOf<String>()
        
        if (brawler.winRate > 0.6f) {
            reasons.add("High win rate (${(brawler.winRate * 100).toInt()}%)")
        }
        
        if (brawler.pickRate > 0.3f) {
            reasons.add("Popular pick")
        }
        
        val mapSynergy = calculateMapSynergy(brawler, draftState.currentMap)
        if (mapSynergy > 0.7f) {
            reasons.add("Strong on ${draftState.currentMap.name}")
        }
        
        return if (reasons.isNotEmpty()) {
            reasons.joinToString(", ")
        } else {
            "Solid ban option"
        }
    }
    
    private fun generatePickReason(brawler: Brawler, draftState: DraftState): String {
        val reasons = mutableListOf<String>()
        
        val mapSynergy = calculateMapSynergy(brawler, draftState.currentMap)
        if (mapSynergy > 0.7f) {
            reasons.add("Excellent on ${draftState.currentMap.name}")
        } else if (mapSynergy > 0.5f) {
            reasons.add("Good map synergy")
        }
        
        val teamSynergy = calculateTeamSynergy(brawler, draftState.getPlayerTeam())
        if (teamSynergy > 0.7f) {
            reasons.add("Great team synergy")
        }
        
        if (brawler.winRate > 0.6f) {
            reasons.add("High win rate")
        }
        
        return if (reasons.isNotEmpty()) {
            reasons.joinToString(", ")
        } else {
            "Solid pick option"
        }
    }
    
    private fun determinePriority(score: Float): RecommendationPriority {
        return when {
            score >= 0.8f -> RecommendationPriority.CRITICAL
            score >= 0.6f -> RecommendationPriority.HIGH
            score >= 0.4f -> RecommendationPriority.MEDIUM
            else -> RecommendationPriority.LOW
        }
    }
}
