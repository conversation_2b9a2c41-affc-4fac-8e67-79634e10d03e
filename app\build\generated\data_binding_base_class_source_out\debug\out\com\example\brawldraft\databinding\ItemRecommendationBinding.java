// Generated by view binder compiler. Do not edit!
package com.example.brawldraft.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.LinearLayout;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.example.brawldraft.R;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class ItemRecommendationBinding implements ViewBinding {
  @NonNull
  private final LinearLayout rootView;

  @NonNull
  public final TextView brawlerName;

  @NonNull
  public final View priorityIndicator;

  @NonNull
  public final TextView reasonText;

  @NonNull
  public final TextView scoreText;

  private ItemRecommendationBinding(@NonNull LinearLayout rootView, @NonNull TextView brawlerName,
      @NonNull View priorityIndicator, @NonNull TextView reasonText, @NonNull TextView scoreText) {
    this.rootView = rootView;
    this.brawlerName = brawlerName;
    this.priorityIndicator = priorityIndicator;
    this.reasonText = reasonText;
    this.scoreText = scoreText;
  }

  @Override
  @NonNull
  public LinearLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static ItemRecommendationBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static ItemRecommendationBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.item_recommendation, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static ItemRecommendationBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.brawler_name;
      TextView brawlerName = ViewBindings.findChildViewById(rootView, id);
      if (brawlerName == null) {
        break missingId;
      }

      id = R.id.priority_indicator;
      View priorityIndicator = ViewBindings.findChildViewById(rootView, id);
      if (priorityIndicator == null) {
        break missingId;
      }

      id = R.id.reason_text;
      TextView reasonText = ViewBindings.findChildViewById(rootView, id);
      if (reasonText == null) {
        break missingId;
      }

      id = R.id.score_text;
      TextView scoreText = ViewBindings.findChildViewById(rootView, id);
      if (scoreText == null) {
        break missingId;
      }

      return new ItemRecommendationBinding((LinearLayout) rootView, brawlerName, priorityIndicator,
          reasonText, scoreText);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
