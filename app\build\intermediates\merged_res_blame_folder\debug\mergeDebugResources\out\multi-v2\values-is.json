{"logs": [{"outputFile": "com.example.brawldraft.app-mergeDebugResources-30:/values-is/values-is.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\2aa33be5c222f8571691eafceec0feae\\transformed\\material-1.10.0\\res\\values-is\\values-is.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,270,344,416,495,577,657,754,869,951,1016,1104,1168,1229,1319,1383,1446,1508,1576,1640,1696,1819,1884,1946,2002,2073,2200,2284,2368,2504,2581,2658,2774,2861,2940,2997,3052,3118,3194,3274,3363,3439,3506,3580,3650,3716,3818,3904,3974,4065,4155,4229,4302,4391,4442,4523,4595,4676,4762,4824,4888,4951,5020,5134,5240,5348,5450,5511,5570", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73", "endColumns": "12,73,71,78,81,79,96,114,81,64,87,63,60,89,63,62,61,67,63,55,122,64,61,55,70,126,83,83,135,76,76,115,86,78,56,54,65,75,79,88,75,66,73,69,65,101,85,69,90,89,73,72,88,50,80,71,80,85,61,63,62,68,113,105,107,101,60,58,79", "endOffsets": "265,339,411,490,572,652,749,864,946,1011,1099,1163,1224,1314,1378,1441,1503,1571,1635,1691,1814,1879,1941,1997,2068,2195,2279,2363,2499,2576,2653,2769,2856,2935,2992,3047,3113,3189,3269,3358,3434,3501,3575,3645,3711,3813,3899,3969,4060,4150,4224,4297,4386,4437,4518,4590,4671,4757,4819,4883,4946,5015,5129,5235,5343,5445,5506,5565,5645"}, "to": {"startLines": "2,33,34,35,36,37,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,2994,3068,3140,3219,3301,4098,4195,4310,4392,4457,4545,4609,4670,4760,4824,4887,4949,5017,5081,5137,5260,5325,5387,5443,5514,5641,5725,5809,5945,6022,6099,6215,6302,6381,6438,6493,6559,6635,6715,6804,6880,6947,7021,7091,7157,7259,7345,7415,7506,7596,7670,7743,7832,7883,7964,8036,8117,8203,8265,8329,8392,8461,8575,8681,8789,8891,8952,9011", "endLines": "5,33,34,35,36,37,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107", "endColumns": "12,73,71,78,81,79,96,114,81,64,87,63,60,89,63,62,61,67,63,55,122,64,61,55,70,126,83,83,135,76,76,115,86,78,56,54,65,75,79,88,75,66,73,69,65,101,85,69,90,89,73,72,88,50,80,71,80,85,61,63,62,68,113,105,107,101,60,58,79", "endOffsets": "315,3063,3135,3214,3296,3376,4190,4305,4387,4452,4540,4604,4665,4755,4819,4882,4944,5012,5076,5132,5255,5320,5382,5438,5509,5636,5720,5804,5940,6017,6094,6210,6297,6376,6433,6488,6554,6630,6710,6799,6875,6942,7016,7086,7152,7254,7340,7410,7501,7591,7665,7738,7827,7878,7959,8031,8112,8198,8260,8324,8387,8456,8570,8676,8784,8886,8947,9006,9086"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\57562ad43921b16639a6438f83b538f4\\transformed\\appcompat-1.6.1\\res\\values-is\\values-is.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,205,302,414,499,600,714,795,874,965,1058,1151,1245,1351,1444,1539,1634,1725,1819,1900,2010,2117,2214,2323,2423,2526,2681,2779", "endColumns": "99,96,111,84,100,113,80,78,90,92,92,93,105,92,94,94,90,93,80,109,106,96,108,99,102,154,97,80", "endOffsets": "200,297,409,494,595,709,790,869,960,1053,1146,1240,1346,1439,1534,1629,1720,1814,1895,2005,2112,2209,2318,2418,2521,2676,2774,2855"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,108", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "320,420,517,629,714,815,929,1010,1089,1180,1273,1366,1460,1566,1659,1754,1849,1940,2034,2115,2225,2332,2429,2538,2638,2741,2896,9091", "endColumns": "99,96,111,84,100,113,80,78,90,92,92,93,105,92,94,94,90,93,80,109,106,96,108,99,102,154,97,80", "endOffsets": "415,512,624,709,810,924,1005,1084,1175,1268,1361,1455,1561,1654,1749,1844,1935,2029,2110,2220,2327,2424,2533,2633,2736,2891,2989,9167"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\27d9032238570e73400cc8465b116304\\transformed\\core-1.10.1\\res\\values-is\\values-is.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,150,257,354,454,557,661,772", "endColumns": "94,106,96,99,102,103,110,100", "endOffsets": "145,252,349,449,552,656,767,868"}, "to": {"startLines": "38,39,40,41,42,43,44,109", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3381,3476,3583,3680,3780,3883,3987,9172", "endColumns": "94,106,96,99,102,103,110,100", "endOffsets": "3471,3578,3675,3775,3878,3982,4093,9268"}}]}]}