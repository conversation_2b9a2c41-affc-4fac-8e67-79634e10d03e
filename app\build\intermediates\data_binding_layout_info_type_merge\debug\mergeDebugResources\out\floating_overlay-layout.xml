<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="floating_overlay" modulePackage="com.example.brawldraft" filePath="app\src\main\res\layout\floating_overlay.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="androidx.constraintlayout.widget.ConstraintLayout"><Targets><Target tag="layout/floating_overlay_0" view="androidx.constraintlayout.widget.ConstraintLayout"><Expressions/><location startLine="1" startOffset="0" endLine="28" endOffset="51"/></Target><Target id="@+id/overlay_text" view="TextView"><Expressions/><location startLine="7" startOffset="4" endLine="17" endOffset="51"/></Target><Target id="@+id/close_button" view="Button"><Expressions/><location startLine="19" startOffset="4" endLine="26" endOffset="55"/></Target></Targets></Layout>