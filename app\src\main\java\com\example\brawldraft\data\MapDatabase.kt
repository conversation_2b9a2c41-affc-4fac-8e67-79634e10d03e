package com.example.brawldraft.data

object MapDatabase {
    
    private val maps = listOf(
        // Gem Grab Maps
        GameMap("hard_rock_mine", "Hard Rock Mine", GameMode.GEM_GRAB),
        GameMap("crystal_arcade", "Crystal Arcade", GameMode.GEM_GRAB),
        GameMap("minecart_madness", "Minecart Madness", GameMode.GEM_GRAB),
        GameMap("double_swoosh", "Double Swoosh", GameMode.GEM_GRAB),
        GameMap("undermine", "Undermine", GameMode.GEM_GRAB),
        
        // Showdown Maps
        GameMap("skull_creek", "Skull Creek", GameMode.SHOWDOWN),
        GameMap("cavern_churn", "Cavern Churn", GameMode.SHOWDOWN),
        GameMap("feast_or_famine", "Feast or Famine", GameMode.SHOWDOWN),
        GameMap("thousand_lakes", "Thousand Lakes", GameMode.SHOWDOWN),
        GameMap("rockwall_brawl", "Rockwall Brawl", GameMode.SHOWDOWN),
        
        // Brawl Ball Maps
        GameMap("sneaky_fields", "Sneaky Fields", GameMode.BRAWL_BALL),
        GameMap("super_stadium", "Super Stadium", GameMode.BRAWL_BALL),
        GameMap("triple_dribble", "Triple Dribble", GameMode.BRAWL_BALL),
        GameMap("center_stage", "Center Stage", GameMode.BRAWL_BALL),
        GameMap("penalty_kick", "Penalty Kick", GameMode.BRAWL_BALL),
        
        // Bounty Maps
        GameMap("dry_season", "Dry Season", GameMode.BOUNTY),
        GameMap("layer_cake", "Layer Cake", GameMode.BOUNTY),
        GameMap("snake_prairie", "Snake Prairie", GameMode.BOUNTY),
        GameMap("shooting_star", "Shooting Star", GameMode.BOUNTY),
        GameMap("excel", "Excel", GameMode.BOUNTY),
        
        // Heist Maps
        GameMap("safe_zone", "Safe Zone", GameMode.HEIST),
        GameMap("kaboom_canyon", "Kaboom Canyon", GameMode.HEIST),
        GameMap("bridge_too_far", "Bridge Too Far", GameMode.HEIST),
        GameMap("hot_potato", "Hot Potato", GameMode.HEIST),
        GameMap("pit_stop", "Pit Stop", GameMode.HEIST),
        
        // Hot Zone Maps
        GameMap("dueling_beetles", "Dueling Beetles", GameMode.HOT_ZONE),
        GameMap("ring_of_fire", "Ring of Fire", GameMode.HOT_ZONE),
        GameMap("parallel_plays", "Parallel Plays", GameMode.HOT_ZONE),
        GameMap("split", "Split", GameMode.HOT_ZONE),
        GameMap("open_business", "Open Business", GameMode.HOT_ZONE),
        
        // Knockout Maps
        GameMap("belle_rock", "Belle Rock", GameMode.KNOCKOUT),
        GameMap("flowing_falls", "Flowing Falls", GameMode.KNOCKOUT),
        GameMap("goldarm_gulch", "Goldarm Gulch", GameMode.KNOCKOUT),
        GameMap("out_in_the_open", "Out in the Open", GameMode.KNOCKOUT),
        GameMap("flaring_phoenix", "Flaring Phoenix", GameMode.KNOCKOUT)
    )
    
    fun getAllMaps(): List<GameMap> = maps
    
    fun getMapById(id: String): GameMap? = maps.find { it.id == id }
    
    fun getMapByName(name: String): GameMap? = maps.find { 
        it.name.equals(name, ignoreCase = true) 
    }
    
    fun getMapsByMode(mode: GameMode): List<GameMap> = maps.filter { it.mode == mode }
    
    fun searchMaps(query: String): List<GameMap> = maps.filter { 
        it.name.contains(query, ignoreCase = true) || 
        it.description.contains(query, ignoreCase = true)
    }
    
    fun detectMapFromName(detectedName: String): GameMap? {
        // Try exact match first
        var map = getMapByName(detectedName)
        if (map != null) return map
        
        // Try partial matches
        val normalizedInput = detectedName.lowercase().replace(Regex("[^a-z0-9]"), "")
        
        for (gameMap in maps) {
            val normalizedMapName = gameMap.name.lowercase().replace(Regex("[^a-z0-9]"), "")
            if (normalizedMapName.contains(normalizedInput) || normalizedInput.contains(normalizedMapName)) {
                return gameMap
            }
        }
        
        // If no match found, create unknown map
        return GameMap(
            id = "unknown_${detectedName.lowercase().replace(" ", "_")}",
            name = detectedName,
            mode = GameMode.UNKNOWN,
            description = "Detected map: $detectedName"
        )
    }
}
