R_DEF: Internal format may change without notice
local
color black
color gaming_accent
color gaming_accent_dark
color gaming_card_bg
color gaming_danger
color gaming_dark_bg
color gaming_success
color gaming_text_primary
color gaming_text_secondary
color gaming_warning
color overlay_bg_semi
color purple_200
color purple_500
color purple_700
color teal_200
color teal_700
color white
drawable bg_circle
drawable bg_overlay_expanded
drawable ic_launcher_background
drawable ic_launcher_foreground
id action_type
id blue_team_brawlers
id brawler_name
id close_button
id draft_status
id draft_teams_container
id launch_overlay_button
id manual_ban_button
id manual_mode_button
id manual_override_container
id manual_pick_button
id minimize_button
id overlay_text
id overlay_title
id priority_indicator
id reason_text
id recommendations_container
id recommendations_recycler
id red_team_brawlers
id score_text
id start_capture_button
id status_indicator
id status_text
layout activity_main
layout floating_overlay
layout item_recommendation
layout item_team_brawler
layout overlay_collapsed
layout overlay_expanded
mipmap ic_launcher
mipmap ic_launcher_round
string app_name
style Theme.Brawldraft
xml backup_rules
xml data_extraction_rules
