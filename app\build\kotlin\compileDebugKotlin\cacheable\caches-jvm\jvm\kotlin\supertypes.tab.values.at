/ Header Record For PersistentHashMapValueStorage kotlin.Enum android.app.Service) (androidx.appcompat.app.AppCompatActivity android.app.Activity android.app.Service kotlin.Enum kotlin.Enum kotlin.Enum kotlin.Enum kotlin.Enum kotlin.Enum- ,com.example.brawldraft.data.DraftStateUpdate- ,com.example.brawldraft.data.DraftStateUpdate- ,com.example.brawldraft.data.DraftStateUpdate- ,com.example.brawldraft.data.DraftStateUpdate- ,com.example.brawldraft.data.DraftStateUpdate- ,com.example.brawldraft.data.DraftStateUpdate) (androidx.recyclerview.widget.ListAdapter5 4androidx.recyclerview.widget.RecyclerView.ViewHolder3 2androidx.recyclerview.widget.DiffUtil.ItemCallback kotlin.Enum kotlin.Enum