<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="overlay_expanded" modulePackage="com.example.brawldraft" filePath="app\src\main\res\layout\overlay_expanded.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="android.widget.LinearLayout"><Targets><Target tag="layout/overlay_expanded_0" view="LinearLayout"><Expressions/><location startLine="1" startOffset="0" endLine="222" endOffset="14"/></Target><Target id="@+id/overlay_title" view="TextView"><Expressions/><location startLine="17" startOffset="8" endLine="25" endOffset="38"/></Target><Target id="@+id/minimize_button" view="ImageButton"><Expressions/><location startLine="27" startOffset="8" endLine="34" endOffset="57"/></Target><Target id="@+id/close_button" view="ImageButton"><Expressions/><location startLine="36" startOffset="8" endLine="42" endOffset="49"/></Target><Target id="@+id/draft_status" view="TextView"><Expressions/><location startLine="47" startOffset="4" endLine="55" endOffset="44"/></Target><Target id="@+id/start_capture_button" view="Button"><Expressions/><location startLine="65" startOffset="8" endLine="73" endOffset="60"/></Target><Target id="@+id/manual_mode_button" view="Button"><Expressions/><location startLine="75" startOffset="8" endLine="82" endOffset="59"/></Target><Target id="@+id/draft_teams_container" view="LinearLayout"><Expressions/><location startLine="87" startOffset="4" endLine="147" endOffset="18"/></Target><Target id="@+id/blue_team_brawlers" view="LinearLayout"><Expressions/><location startLine="113" startOffset="12" endLine="117" endOffset="48"/></Target><Target id="@+id/red_team_brawlers" view="LinearLayout"><Expressions/><location startLine="139" startOffset="12" endLine="143" endOffset="48"/></Target><Target id="@+id/recommendations_container" view="LinearLayout"><Expressions/><location startLine="150" startOffset="4" endLine="173" endOffset="18"/></Target><Target id="@+id/recommendations_recycler" view="androidx.recyclerview.widget.RecyclerView"><Expressions/><location startLine="167" startOffset="8" endLine="171" endOffset="47"/></Target><Target id="@+id/manual_override_container" view="LinearLayout"><Expressions/><location startLine="176" startOffset="4" endLine="220" endOffset="18"/></Target><Target id="@+id/manual_ban_button" view="Button"><Expressions/><location startLine="198" startOffset="12" endLine="206" endOffset="63"/></Target><Target id="@+id/manual_pick_button" view="Button"><Expressions/><location startLine="208" startOffset="12" endLine="216" endOffset="64"/></Target></Targets></Layout>