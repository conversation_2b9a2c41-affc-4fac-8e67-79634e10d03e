package com.example.brawldraft

import android.annotation.SuppressLint
import android.app.Activity
import android.app.Service
import android.content.BroadcastReceiver
import android.content.Context
import android.content.Intent
import android.content.IntentFilter
import android.graphics.PixelFormat
import android.media.projection.MediaProjectionManager
import android.os.Build
import android.os.IBinder
import android.view.Gravity
import android.view.LayoutInflater
import android.view.MotionEvent
import android.view.View
import android.view.WindowManager
import android.widget.Button
import android.widget.FrameLayout
import android.widget.LinearLayout
import android.widget.TextView
import android.widget.Toast
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.example.brawldraft.data.DraftPhase
import com.example.brawldraft.ui.RecommendationAdapter
import kotlinx.coroutines.*
import kotlinx.coroutines.flow.collect

class FloatingOverlayService : Service() {

    private lateinit var windowManager: WindowManager
    private lateinit var floatingView: FrameLayout
    private lateinit var collapsedView: View
    private lateinit var expandedView: View
    private lateinit var params: WindowManager.LayoutParams
    private lateinit var screenCaptureManager: ScreenCaptureManager
    private lateinit var draftStateManager: DraftStateManager
    private lateinit var draftStatusText: TextView
    private lateinit var startCaptureButton: Button

    // Enhanced UI elements
    private lateinit var draftTeamsContainer: LinearLayout
    private lateinit var recommendationsContainer: LinearLayout
    private lateinit var manualOverrideContainer: LinearLayout
    private lateinit var recommendationsRecycler: RecyclerView
    private lateinit var recommendationAdapter: RecommendationAdapter
    private lateinit var blueTeamBrawlers: LinearLayout
    private lateinit var redTeamBrawlers: LinearLayout

    private var isCaptureActive = false
    private val serviceScope = CoroutineScope(Dispatchers.Main + SupervisorJob())

    private val screenCaptureReceiver = object : BroadcastReceiver() {
        override fun onReceive(context: Context?, intent: Intent?) {
            if (intent?.action == "com.example.brawldraft.SCREEN_CAPTURE_RESULT") {
                val resultCode = intent.getIntExtra("result_code", Activity.RESULT_CANCELED)
                val resultData = intent.getParcelableExtra<Intent>("result_data")
                startScreenCapture(resultCode, resultData)
            }
        }
    }

    private val analysisReceiver = object : BroadcastReceiver() {
        override fun onReceive(context: Context?, intent: Intent?) {
            if (intent?.action == "com.example.brawldraft.ANALYSIS_RESULT") {
                handleAnalysisResult(intent)
            }
        }
    }

    override fun onBind(intent: Intent?): IBinder? {
        return null
    }

    @SuppressLint("ClickableViewAccessibility")
    override fun onCreate() {
        super.onCreate()
        floatingView = FrameLayout(this)
        screenCaptureManager = ScreenCaptureManager(this)
        draftStateManager = DraftStateManager()

        val inflater = LayoutInflater.from(this)
        collapsedView = inflater.inflate(R.layout.overlay_collapsed, floatingView, false)
        expandedView = inflater.inflate(R.layout.overlay_expanded, floatingView, false)

        floatingView.addView(collapsedView)
        floatingView.addView(expandedView)
        expandedView.visibility = View.GONE

        // Initialize UI elements
        draftStatusText = expandedView.findViewById(R.id.draft_status)
        startCaptureButton = expandedView.findViewById(R.id.start_capture_button)

        // Initialize enhanced UI elements
        draftTeamsContainer = expandedView.findViewById(R.id.draft_teams_container)
        recommendationsContainer = expandedView.findViewById(R.id.recommendations_container)
        manualOverrideContainer = expandedView.findViewById(R.id.manual_override_container)
        recommendationsRecycler = expandedView.findViewById(R.id.recommendations_recycler)
        blueTeamBrawlers = expandedView.findViewById(R.id.blue_team_brawlers)
        redTeamBrawlers = expandedView.findViewById(R.id.red_team_brawlers)

        // Setup recommendations RecyclerView
        recommendationAdapter = RecommendationAdapter { recommendation ->
            // Handle recommendation click
            Toast.makeText(this, "Selected: ${recommendation.brawler.name}", Toast.LENGTH_SHORT).show()
        }
        recommendationsRecycler.adapter = recommendationAdapter
        recommendationsRecycler.layoutManager = LinearLayoutManager(this)

        // Register broadcast receivers
        val captureFilter = IntentFilter("com.example.brawldraft.SCREEN_CAPTURE_RESULT")
        registerReceiver(screenCaptureReceiver, captureFilter)

        val analysisFilter = IntentFilter("com.example.brawldraft.ANALYSIS_RESULT")
        registerReceiver(analysisReceiver, analysisFilter)

        // Observe draft state changes
        serviceScope.launch {
            draftStateManager.draftState.collect { draftState ->
                updateDraftStatusUI(draftState)
                updateDraftTeamsUI(draftState)
            }
        }

        // Observe recommendations
        serviceScope.launch {
            draftStateManager.recommendations.collect { recommendations ->
                updateRecommendationsUI(recommendations)
            }
        }

        params = WindowManager.LayoutParams(
            WindowManager.LayoutParams.WRAP_CONTENT,
            WindowManager.LayoutParams.WRAP_CONTENT,
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
                WindowManager.LayoutParams.TYPE_APPLICATION_OVERLAY
            } else {
                WindowManager.LayoutParams.TYPE_PHONE
            },
            WindowManager.LayoutParams.FLAG_NOT_FOCUSABLE,
            PixelFormat.TRANSLUCENT
        )

        params.gravity = Gravity.TOP or Gravity.START
        params.x = 0
        params.y = 100

        windowManager = getSystemService(WINDOW_SERVICE) as WindowManager
        windowManager.addView(floatingView, params)

        collapsedView.setOnClickListener {
            switchToExpandedView()
        }

        expandedView.findViewById<View>(R.id.minimize_button).setOnClickListener {
            switchToCollapsedView()
        }

        expandedView.findViewById<View>(R.id.close_button).setOnClickListener {
            stopSelf()
        }

        startCaptureButton.setOnClickListener {
            if (!isCaptureActive) {
                requestScreenCapturePermission()
            } else {
                stopScreenCapture()
            }
        }

        expandedView.findViewById<Button>(R.id.manual_mode_button).setOnClickListener {
            toggleManualMode()
        }

        // Test button to simulate draft data
        expandedView.findViewById<Button>(R.id.start_capture_button).setOnLongClickListener {
            simulateTestDraft()
            true
        }

        // Manual override buttons
        expandedView.findViewById<Button>(R.id.manual_ban_button).setOnClickListener {
            // TODO: Show brawler selection for ban
            Toast.makeText(this, "Manual ban - coming soon!", Toast.LENGTH_SHORT).show()
        }

        expandedView.findViewById<Button>(R.id.manual_pick_button).setOnClickListener {
            // TODO: Show brawler selection for pick
            Toast.makeText(this, "Manual pick - coming soon!", Toast.LENGTH_SHORT).show()
        }

        floatingView.setOnTouchListener(object : View.OnTouchListener {
            private var initialX: Int = 0
            private var initialY: Int = 0
            private var initialTouchX: Float = 0.toFloat()
            private var initialTouchY: Float = 0.toFloat()

            override fun onTouch(v: View, event: MotionEvent): Boolean {
                when (event.action) {
                    MotionEvent.ACTION_DOWN -> {
                        initialX = params.x
                        initialY = params.y
                        initialTouchX = event.rawX
                        initialTouchY = event.rawY
                        return true
                    }
                    MotionEvent.ACTION_UP -> {
                        return true
                    }
                    MotionEvent.ACTION_MOVE -> {
                        params.x = initialX + (event.rawX - initialTouchX).toInt()
                        params.y = initialY + (event.rawY - initialTouchY).toInt()
                        windowManager.updateViewLayout(floatingView, params)
                        return true
                    }
                }
                return false
            }
        })
    }

    private fun switchToCollapsedView() {
        collapsedView.visibility = View.VISIBLE
        expandedView.visibility = View.GONE
    }

    private fun switchToExpandedView() {
        collapsedView.visibility = View.GONE
        expandedView.visibility = View.VISIBLE
    }

    private fun requestScreenCapturePermission() {
        try {
            val mediaProjectionManager = getSystemService(MEDIA_PROJECTION_SERVICE) as MediaProjectionManager
            val captureIntent = mediaProjectionManager.createScreenCaptureIntent()

            // Since we can't start activity for result from a service, we'll use a different approach
            // We'll create a transparent activity to handle the permission request
            val intent = Intent(this, ScreenCapturePermissionActivity::class.java).apply {
                addFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
                putExtra("capture_intent", captureIntent)
            }
            startActivity(intent)

        } catch (e: Exception) {
            Toast.makeText(this, "Failed to request screen capture permission", Toast.LENGTH_SHORT).show()
            draftStatusText.text = "Failed to start screen capture"
        }
    }

    private fun startScreenCapture(resultCode: Int, resultData: Intent?) {
        if (resultCode == Activity.RESULT_OK && resultData != null) {
            screenCaptureManager.startScreenCapture(resultCode, resultData)
            isCaptureActive = true
            updateCaptureUI()
            draftStatusText.text = "Screen capture active - analyzing draft..."
        } else {
            Toast.makeText(this, "Screen capture permission denied", Toast.LENGTH_SHORT).show()
            draftStatusText.text = "Screen capture permission denied"
        }
    }

    private fun stopScreenCapture() {
        screenCaptureManager.stopScreenCapture()
        isCaptureActive = false
        updateCaptureUI()
        draftStatusText.text = "Screen capture stopped"
    }

    private fun updateCaptureUI() {
        if (isCaptureActive) {
            startCaptureButton.text = "Stop Capture"
            startCaptureButton.backgroundTintList = getColorStateList(R.color.gaming_danger)
        } else {
            startCaptureButton.text = "Start Capture"
            startCaptureButton.backgroundTintList = getColorStateList(R.color.gaming_success)
        }
    }

    private fun updateDraftStatusUI(draftState: com.example.brawldraft.data.DraftState) {
        val statusText = when (draftState.currentPhase) {
            DraftPhase.WAITING -> "Waiting for draft to start..."
            DraftPhase.BAN_PHASE -> {
                val bansRemaining = 6 - draftState.getTotalBans()
                "Ban Phase - $bansRemaining bans remaining"
            }
            DraftPhase.PICK_PHASE_1 -> "Pick Phase 1 - Blue team picks"
            DraftPhase.PICK_PHASE_2 -> "Pick Phase 2 - Red team picks"
            DraftPhase.PICK_PHASE_3 -> "Pick Phase 3 - Blue team picks"
            DraftPhase.PICK_PHASE_4 -> "Pick Phase 4 - Red team picks"
            DraftPhase.COMPLETED -> "Draft completed!"
        }

        draftStatusText.text = if (draftState.currentMap.name != "Unknown Map") {
            "$statusText\nMap: ${draftState.currentMap.name}"
        } else {
            statusText
        }
    }

    private fun handleAnalysisResult(intent: Intent) {
        val gameState = intent.getStringExtra("game_state") ?: "UNKNOWN"
        val confidence = intent.getFloatExtra("confidence", 0.0f)
        val detectedBrawlersCount = intent.getIntExtra("detected_brawlers_count", 0)
        val currentPhase = intent.getStringExtra("current_phase") ?: "WAITING"
        val detectedMap = intent.getStringExtra("detected_map")

        // Update UI with analysis information
        val analysisInfo = buildString {
            append("Analysis: $gameState")
            if (confidence > 0) {
                append(" (${(confidence * 100).toInt()}%)")
            }
            if (detectedBrawlersCount > 0) {
                append("\nDetected: $detectedBrawlersCount brawlers")
            }
            if (detectedMap != null) {
                append("\nMap: $detectedMap")
            }
        }

        // Update the draft status with analysis info if capture is active
        if (isCaptureActive) {
            draftStatusText.text = analysisInfo
        }

        // TODO: Process detected brawlers and update draft state
        // This would involve parsing the analysis results and updating the DraftStateManager
    }

    private fun updateDraftTeamsUI(draftState: com.example.brawldraft.data.DraftState) {
        // Show/hide teams container based on draft state
        if (draftState.currentPhase != com.example.brawldraft.data.DraftPhase.WAITING) {
            draftTeamsContainer.visibility = View.VISIBLE

            // Update blue team
            updateTeamBrawlersUI(blueTeamBrawlers, draftState.blueTeam)

            // Update red team
            updateTeamBrawlersUI(redTeamBrawlers, draftState.redTeam)
        } else {
            draftTeamsContainer.visibility = View.GONE
        }
    }

    private fun updateTeamBrawlersUI(container: LinearLayout, teamState: com.example.brawldraft.data.TeamState) {
        container.removeAllViews()

        // Add picked brawlers
        for (brawler in teamState.pickedBrawlers) {
            val brawlerView = createTeamBrawlerView(brawler.name, "P", true)
            container.addView(brawlerView)
        }

        // Add banned brawlers
        for (brawler in teamState.bannedBrawlers) {
            val brawlerView = createTeamBrawlerView(brawler.name, "B", false)
            container.addView(brawlerView)
        }
    }

    private fun createTeamBrawlerView(brawlerName: String, actionType: String, isPick: Boolean): View {
        val inflater = LayoutInflater.from(this)
        val view = inflater.inflate(R.layout.item_team_brawler, null)

        val nameText = view.findViewById<TextView>(R.id.brawler_name)
        val actionText = view.findViewById<TextView>(R.id.action_type)
        val statusIndicator = view.findViewById<View>(R.id.status_indicator)

        nameText.text = brawlerName
        actionText.text = actionType

        val color = if (isPick) {
            getColor(R.color.gaming_success)
        } else {
            getColor(R.color.gaming_danger)
        }
        statusIndicator.setBackgroundColor(color)

        return view
    }

    private fun updateRecommendationsUI(recommendations: List<BrawlerRecommendation>) {
        // Only update if there's a meaningful change to avoid unnecessary UI updates
        if (recommendations.isNotEmpty() && recommendations.size >= 3) {
            recommendationsContainer.visibility = View.VISIBLE
            recommendationAdapter.submitList(recommendations.take(5)) // Limit to top 5
        } else {
            recommendationsContainer.visibility = View.GONE
        }
    }

    private fun toggleManualMode() {
        if (manualOverrideContainer.visibility == View.VISIBLE) {
            manualOverrideContainer.visibility = View.GONE
        } else {
            manualOverrideContainer.visibility = View.VISIBLE
        }
    }

    private fun simulateTestDraft() {
        // Simulate some draft data for testing UI
        val shelly = com.example.brawldraft.data.BrawlerDatabase.getBrawlerByName("Shelly")
        val colt = com.example.brawldraft.data.BrawlerDatabase.getBrawlerByName("Colt")
        val poco = com.example.brawldraft.data.BrawlerDatabase.getBrawlerByName("Poco")

        if (shelly != null && colt != null && poco != null) {
            draftStateManager.manualBrawlerAction(shelly, com.example.brawldraft.data.TeamSide.BLUE, com.example.brawldraft.data.ActionType.PICK)
            draftStateManager.manualBrawlerAction(colt, com.example.brawldraft.data.TeamSide.RED, com.example.brawldraft.data.ActionType.PICK)
            draftStateManager.manualBrawlerAction(poco, com.example.brawldraft.data.TeamSide.BLUE, com.example.brawldraft.data.ActionType.BAN)

            draftStateManager.updateDraftState(com.example.brawldraft.data.DraftStateUpdate.PhaseChanged(com.example.brawldraft.data.DraftPhase.PICK_PHASE_1))

            Toast.makeText(this, "Test draft data loaded! Long press again to reset.", Toast.LENGTH_SHORT).show()
        }
    }

    override fun onDestroy() {
        super.onDestroy()
        if (isCaptureActive) {
            stopScreenCapture()
        }
        try {
            unregisterReceiver(screenCaptureReceiver)
            unregisterReceiver(analysisReceiver)
        } catch (e: Exception) {
            // Receivers might not be registered
        }
        serviceScope.cancel()
        if (::floatingView.isInitialized) {
            windowManager.removeView(floatingView)
        }
    }
}