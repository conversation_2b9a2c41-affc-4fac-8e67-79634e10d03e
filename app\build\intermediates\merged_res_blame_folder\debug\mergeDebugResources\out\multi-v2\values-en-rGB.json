{"logs": [{"outputFile": "com.example.brawldraft.app-mergeDebugResources-30:/values-en-rGB/values-en-rGB.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\57562ad43921b16639a6438f83b538f4\\transformed\\appcompat-1.6.1\\res\\values-en-rGB\\values-en-rGB.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,209,309,417,501,601,716,794,869,960,1053,1148,1242,1342,1435,1530,1624,1715,1806,1888,1991,2094,2193,2298,2402,2506,2662,2762", "endColumns": "103,99,107,83,99,114,77,74,90,92,94,93,99,92,94,93,90,90,81,102,102,98,104,103,103,155,99,82", "endOffsets": "204,304,412,496,596,711,789,864,955,1048,1143,1237,1337,1430,1525,1619,1710,1801,1883,1986,2089,2188,2293,2397,2501,2657,2757,2840"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,108", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "322,426,526,634,718,818,933,1011,1086,1177,1270,1365,1459,1559,1652,1747,1841,1932,2023,2105,2208,2311,2410,2515,2619,2723,2879,9020", "endColumns": "103,99,107,83,99,114,77,74,90,92,94,93,99,92,94,93,90,90,81,102,102,98,104,103,103,155,99,82", "endOffsets": "421,521,629,713,813,928,1006,1081,1172,1265,1360,1454,1554,1647,1742,1836,1927,2018,2100,2203,2306,2405,2510,2614,2718,2874,2974,9098"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\27d9032238570e73400cc8465b116304\\transformed\\core-1.10.1\\res\\values-en-rGB\\values-en-rGB.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,151,253,352,451,555,658,774", "endColumns": "95,101,98,98,103,102,115,100", "endOffsets": "146,248,347,446,550,653,769,870"}, "to": {"startLines": "38,39,40,41,42,43,44,109", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3386,3482,3584,3683,3782,3886,3989,9103", "endColumns": "95,101,98,98,103,102,115,100", "endOffsets": "3477,3579,3678,3777,3881,3984,4100,9199"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\2aa33be5c222f8571691eafceec0feae\\transformed\\material-1.10.0\\res\\values-en-rGB\\values-en-rGB.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,272,354,432,509,595,679,777,892,971,1036,1126,1193,1252,1342,1406,1470,1533,1602,1666,1720,1832,1890,1952,2006,2078,2200,2287,2368,2508,2585,2666,2793,2884,2961,3015,3066,3132,3202,3279,3366,3441,3512,3589,3658,3727,3834,3925,3997,4086,4175,4249,4321,4407,4457,4536,4602,4682,4766,4828,4892,4955,5024,5124,5219,5311,5403,5461,5516", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73", "endColumns": "12,81,77,76,85,83,97,114,78,64,89,66,58,89,63,63,62,68,63,53,111,57,61,53,71,121,86,80,139,76,80,126,90,76,53,50,65,69,76,86,74,70,76,68,68,106,90,71,88,88,73,71,85,49,78,65,79,83,61,63,62,68,99,94,91,91,57,54,77", "endOffsets": "267,349,427,504,590,674,772,887,966,1031,1121,1188,1247,1337,1401,1465,1528,1597,1661,1715,1827,1885,1947,2001,2073,2195,2282,2363,2503,2580,2661,2788,2879,2956,3010,3061,3127,3197,3274,3361,3436,3507,3584,3653,3722,3829,3920,3992,4081,4170,4244,4316,4402,4452,4531,4597,4677,4761,4823,4887,4950,5019,5119,5214,5306,5398,5456,5511,5589"}, "to": {"startLines": "2,33,34,35,36,37,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,2979,3061,3139,3216,3302,4105,4203,4318,4397,4462,4552,4619,4678,4768,4832,4896,4959,5028,5092,5146,5258,5316,5378,5432,5504,5626,5713,5794,5934,6011,6092,6219,6310,6387,6441,6492,6558,6628,6705,6792,6867,6938,7015,7084,7153,7260,7351,7423,7512,7601,7675,7747,7833,7883,7962,8028,8108,8192,8254,8318,8381,8450,8550,8645,8737,8829,8887,8942", "endLines": "5,33,34,35,36,37,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107", "endColumns": "12,81,77,76,85,83,97,114,78,64,89,66,58,89,63,63,62,68,63,53,111,57,61,53,71,121,86,80,139,76,80,126,90,76,53,50,65,69,76,86,74,70,76,68,68,106,90,71,88,88,73,71,85,49,78,65,79,83,61,63,62,68,99,94,91,91,57,54,77", "endOffsets": "317,3056,3134,3211,3297,3381,4198,4313,4392,4457,4547,4614,4673,4763,4827,4891,4954,5023,5087,5141,5253,5311,5373,5427,5499,5621,5708,5789,5929,6006,6087,6214,6305,6382,6436,6487,6553,6623,6700,6787,6862,6933,7010,7079,7148,7255,7346,7418,7507,7596,7670,7742,7828,7878,7957,8023,8103,8187,8249,8313,8376,8445,8545,8640,8732,8824,8882,8937,9015"}}]}]}