<?xml version="1.0" encoding="utf-8"?>
<resources>
    <color name="black">#FF000000</color>
    <color name="gaming_accent">#FF00D4FF</color>
    <color name="gaming_accent_dark">#FF0099CC</color>
    <color name="gaming_card_bg">#FF2D2D2D</color>
    <color name="gaming_danger">#FFFF4444</color>
    <color name="gaming_dark_bg">#FF1A1A1A</color>
    <color name="gaming_success">#FF00FF88</color>
    <color name="gaming_text_primary">#FFFFFFFF</color>
    <color name="gaming_text_secondary">#FFCCCCCC</color>
    <color name="gaming_warning">#FFFFAA00</color>
    <color name="overlay_bg_semi">#E6000000</color>
    <color name="purple_200">#FFBB86FC</color>
    <color name="purple_500">#FF6200EE</color>
    <color name="purple_700">#FF3700B3</color>
    <color name="teal_200">#FF03DAC5</color>
    <color name="teal_700">#FF018786</color>
    <color name="white">#FFFFFFFF</color>
    <string name="app_name">Brawldraft</string>
    <style name="Theme.Brawldraft" parent="Theme.MaterialComponents.DayNight.DarkActionBar">
        
        <item name="colorPrimary">@color/purple_500</item>
        <item name="colorPrimaryVariant">@color/purple_700</item>
        <item name="colorOnPrimary">@color/white</item>
        
        <item name="colorSecondary">@color/teal_200</item>
        <item name="colorSecondaryVariant">@color/teal_700</item>
        <item name="colorOnSecondary">@color/black</item>
        
        <item name="android:statusBarColor">?attr/colorPrimaryVariant</item>
        
    </style>
</resources>