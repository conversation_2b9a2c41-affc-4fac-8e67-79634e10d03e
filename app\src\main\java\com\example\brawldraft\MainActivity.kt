package com.example.brawldraft

import android.content.Intent
import android.net.Uri
import android.os.Build
import android.os.Bundle
import android.provider.Settings
import android.widget.Button
import android.widget.TextView
import android.widget.Toast
import androidx.activity.result.contract.ActivityResultContracts
import androidx.appcompat.app.AppCompatActivity
import org.opencv.android.OpenCVLoader

class MainActivity : AppCompatActivity() {

    private lateinit var statusText: TextView

    private val overlayPermissionLauncher = registerForActivityResult(ActivityResultContracts.StartActivityForResult()) {
        if (isOverlayPermissionGranted()) {
            startOverlayService()
        } else {
            statusText.text = "Overlay permission denied. Please grant permission to continue."
            Toast.makeText(this, "Overlay permission is required to use the draft helper.", Toast.LENGTH_LONG).show()
        }
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(R.layout.activity_main)

        statusText = findViewById(R.id.status_text)

        if (!OpenCVLoader.initDebug()) {
            statusText.text = "OpenCV initialization failed!"
            Toast.makeText(this, "OpenCV initialization failed!", Toast.LENGTH_LONG).show()
        } else {
            statusText.text = "OpenCV initialized successfully. Ready to launch overlay."
        }

        findViewById<Button>(R.id.launch_overlay_button).setOnClickListener {
            if (isOverlayPermissionGranted()) {
                startOverlayService()
            } else {
                statusText.text = "Requesting overlay permission..."
                requestOverlayPermission()
            }
        }
    }

    private fun isOverlayPermissionGranted(): Boolean {
        return if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
            Settings.canDrawOverlays(this)
        } else {
            true
        }
    }

    private fun requestOverlayPermission() {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
            val intent = Intent(
                Settings.ACTION_MANAGE_OVERLAY_PERMISSION,
                Uri.parse("package:$packageName")
            )
            overlayPermissionLauncher.launch(intent)
        }
    }

    private fun startOverlayService() {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
            if (Settings.canDrawOverlays(this)) {
                startService(Intent(this, FloatingOverlayService::class.java))
                statusText.text = "Overlay launched successfully! You can now minimize this app."
            }
        } else {
            startService(Intent(this, FloatingOverlayService::class.java))
            statusText.text = "Overlay launched successfully! You can now minimize this app."
        }
    }
}