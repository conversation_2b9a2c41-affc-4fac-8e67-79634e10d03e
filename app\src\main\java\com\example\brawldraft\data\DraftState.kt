package com.example.brawldraft.data

data class DraftState(
    val currentPhase: DraftPhase = DraftPhase.WAITING,
    val currentMap: GameMap = GameMap.unknown(),
    val blueTeam: TeamState = TeamState(),
    val redTeam: TeamState = TeamState(),
    val bannedBrawlers: List<Brawler> = emptyList(),
    val availableBrawlers: List<Brawler> = emptyList(),
    val draftHistory: List<DraftAction> = emptyList(),
    val isPlayerBlueTeam: Boolean = true, // Assume player is on blue team by default
    val lastUpdated: Long = System.currentTimeMillis()
) {
    
    fun getPlayerTeam(): TeamState = if (isPlayerBlueTeam) blueTeam else redTeam
    fun getEnemyTeam(): TeamState = if (isPlayerBlueTeam) redTeam else blueTeam
    
    fun getPlayerSide(): TeamSide = if (isPlayerBlueTeam) TeamSide.BLUE else TeamSide.RED
    fun getEnemySide(): TeamSide = if (isPlayerBlueTeam) TeamSide.RED else TeamSide.BLUE
    
    fun isBrawlerBanned(brawler: Brawler): Boolean = bannedBrawlers.any { it.id == brawler.id }
    fun isBrawlerPicked(brawler: Brawler): Boolean = 
        blueTeam.pickedBrawlers.any { it.id == brawler.id } || 
        redTeam.pickedBrawlers.any { it.id == brawler.id }
    
    fun isBrawlerAvailable(brawler: Brawler): Boolean = 
        !isBrawlerBanned(brawler) && !isBrawlerPicked(brawler)
    
    fun getTotalBans(): Int = bannedBrawlers.size
    fun getTotalPicks(): Int = blueTeam.pickedBrawlers.size + redTeam.pickedBrawlers.size
    
    fun isComplete(): Boolean = currentPhase == DraftPhase.COMPLETED
    
    fun getNextExpectedAction(): ExpectedAction? {
        return when (currentPhase) {
            DraftPhase.WAITING -> null
            DraftPhase.BAN_PHASE -> {
                val totalBans = getTotalBans()
                if (totalBans < 6) { // 6 total bans (3 per team)
                    ExpectedAction(ActionType.BAN, determineNextTeam())
                } else null
            }
            DraftPhase.PICK_PHASE_1 -> {
                if (blueTeam.pickedBrawlers.isEmpty()) {
                    ExpectedAction(ActionType.PICK, TeamSide.BLUE)
                } else null
            }
            DraftPhase.PICK_PHASE_2 -> {
                if (redTeam.pickedBrawlers.size < 2) {
                    ExpectedAction(ActionType.PICK, TeamSide.RED)
                } else null
            }
            DraftPhase.PICK_PHASE_3 -> {
                if (blueTeam.pickedBrawlers.size < 3) {
                    ExpectedAction(ActionType.PICK, TeamSide.BLUE)
                } else null
            }
            DraftPhase.PICK_PHASE_4 -> {
                if (redTeam.pickedBrawlers.size < 3) {
                    ExpectedAction(ActionType.PICK, TeamSide.RED)
                } else null
            }
            DraftPhase.COMPLETED -> null
        }
    }
    
    private fun determineNextTeam(): TeamSide {
        // For ban phase, alternate between teams
        val totalBans = getTotalBans()
        return if (totalBans % 2 == 0) TeamSide.BLUE else TeamSide.RED
    }
}

data class TeamState(
    val pickedBrawlers: List<Brawler> = emptyList(),
    val bannedBrawlers: List<Brawler> = emptyList() // Brawlers this team banned
) {
    fun isComplete(): Boolean = pickedBrawlers.size == 3
    fun hasPickedBrawler(brawler: Brawler): Boolean = pickedBrawlers.any { it.id == brawler.id }
    fun hasBannedBrawler(brawler: Brawler): Boolean = bannedBrawlers.any { it.id == brawler.id }
}

data class ExpectedAction(
    val actionType: ActionType,
    val team: TeamSide
)

// Draft state update events
sealed class DraftStateUpdate {
    data class PhaseChanged(val newPhase: DraftPhase) : DraftStateUpdate()
    data class MapDetected(val map: GameMap) : DraftStateUpdate()
    data class BrawlerBanned(val brawler: Brawler, val team: TeamSide) : DraftStateUpdate()
    data class BrawlerPicked(val brawler: Brawler, val team: TeamSide) : DraftStateUpdate()
    data class DraftReset() : DraftStateUpdate()
    data class ManualCorrection(val action: DraftAction, val isUndo: Boolean = false) : DraftStateUpdate()
}
