package com.example.brawldraft

import android.app.Notification
import android.app.NotificationChannel
import android.app.NotificationManager
import android.app.Service
import android.content.Context
import android.content.Intent
import android.graphics.Bitmap
import android.graphics.PixelFormat
import android.hardware.display.DisplayManager
import android.hardware.display.VirtualDisplay
import android.media.Image
import android.media.ImageReader
import android.media.projection.MediaProjection
import android.media.projection.MediaProjectionManager
import android.os.Build
import android.os.Handler
import android.os.IBinder
import android.os.Looper
import android.util.DisplayMetrics
import android.util.Log
import android.view.WindowManager
import androidx.core.app.NotificationCompat
import com.example.brawldraft.vision.ImageAnalyzer
import com.example.brawldraft.vision.TemplateMatcher
import kotlinx.coroutines.*
import java.nio.ByteBuffer

class ScreenCaptureService : Service() {

    companion object {
        private const val TAG = "ScreenCaptureService"
        private const val NOTIFICATION_ID = 1001
        private const val CHANNEL_ID = "screen_capture_channel"
        private const val CAPTURE_INTERVAL_MS = 1000L // 1 second intervals
        
        const val EXTRA_RESULT_CODE = "result_code"
        const val EXTRA_RESULT_DATA = "result_data"
        
        const val ACTION_START_CAPTURE = "start_capture"
        const val ACTION_STOP_CAPTURE = "stop_capture"
    }

    private var mediaProjection: MediaProjection? = null
    private var virtualDisplay: VirtualDisplay? = null
    private var imageReader: ImageReader? = null
    private var mediaProjectionManager: MediaProjectionManager? = null
    
    private var screenWidth = 0
    private var screenHeight = 0
    private var screenDensity = 0
    
    private val serviceScope = CoroutineScope(Dispatchers.Default + SupervisorJob())
    private var captureJob: Job? = null

    private val captureHandler = Handler(Looper.getMainLooper())
    private val imageAnalyzer = ImageAnalyzer()
    private val templateMatcher = TemplateMatcher()

    override fun onBind(intent: Intent?): IBinder? = null

    override fun onCreate() {
        super.onCreate()
        createNotificationChannel()
        getScreenMetrics()
        mediaProjectionManager = getSystemService(Context.MEDIA_PROJECTION_SERVICE) as MediaProjectionManager

        // Initialize template matcher
        templateMatcher.initialize(this)
    }

    override fun onStartCommand(intent: Intent?, flags: Int, startId: Int): Int {
        when (intent?.action) {
            ACTION_START_CAPTURE -> {
                val resultCode = intent.getIntExtra(EXTRA_RESULT_CODE, -1)
                val resultData = intent.getParcelableExtra<Intent>(EXTRA_RESULT_DATA)
                
                if (resultCode != -1 && resultData != null) {
                    startCapture(resultCode, resultData)
                } else {
                    Log.e(TAG, "Invalid result code or data for screen capture")
                    stopSelf()
                }
            }
            ACTION_STOP_CAPTURE -> {
                stopCapture()
                stopSelf()
            }
        }
        
        return START_NOT_STICKY
    }

    private fun createNotificationChannel() {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            val channel = NotificationChannel(
                CHANNEL_ID,
                "Screen Capture Service",
                NotificationManager.IMPORTANCE_LOW
            ).apply {
                description = "Captures screen for draft analysis"
                setShowBadge(false)
            }
            
            val notificationManager = getSystemService(NotificationManager::class.java)
            notificationManager.createNotificationChannel(channel)
        }
    }

    private fun getScreenMetrics() {
        val windowManager = getSystemService(Context.WINDOW_SERVICE) as WindowManager
        val displayMetrics = DisplayMetrics()
        
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.R) {
            val display = windowManager.defaultDisplay
            display.getRealMetrics(displayMetrics)
        } else {
            @Suppress("DEPRECATION")
            windowManager.defaultDisplay.getRealMetrics(displayMetrics)
        }
        
        screenWidth = displayMetrics.widthPixels
        screenHeight = displayMetrics.heightPixels
        screenDensity = displayMetrics.densityDpi
        
        Log.d(TAG, "Screen metrics: ${screenWidth}x${screenHeight}, density: $screenDensity")
    }

    private fun startCapture(resultCode: Int, resultData: Intent) {
        try {
            startForeground(NOTIFICATION_ID, createNotification())
            
            mediaProjection = mediaProjectionManager?.getMediaProjection(resultCode, resultData)
            
            imageReader = ImageReader.newInstance(
                screenWidth, 
                screenHeight, 
                PixelFormat.RGBA_8888, 
                2
            )
            
            virtualDisplay = mediaProjection?.createVirtualDisplay(
                "BrawlDraftCapture",
                screenWidth,
                screenHeight,
                screenDensity,
                DisplayManager.VIRTUAL_DISPLAY_FLAG_AUTO_MIRROR,
                imageReader?.surface,
                null,
                null
            )
            
            imageReader?.setOnImageAvailableListener({ reader ->
                captureHandler.post {
                    processImage(reader)
                }
            }, captureHandler)
            
            // Start periodic capture processing
            startPeriodicCapture()
            
            Log.d(TAG, "Screen capture started successfully")
            
        } catch (e: Exception) {
            Log.e(TAG, "Failed to start screen capture", e)
            stopCapture()
            stopSelf()
        }
    }

    private fun startPeriodicCapture() {
        captureJob = serviceScope.launch {
            while (isActive) {
                delay(CAPTURE_INTERVAL_MS)
                // The actual image processing happens in the ImageReader callback
                // This just ensures we maintain the capture interval
            }
        }
    }

    private fun processImage(reader: ImageReader) {
        var image: Image? = null
        try {
            image = reader.acquireLatestImage()
            if (image != null) {
                // Convert image to bitmap for processing
                val bitmap = imageToBitmap(image)
                if (bitmap != null) {
                    // Process the bitmap for draft detection
                    serviceScope.launch {
                        processCapturedBitmap(bitmap)
                    }
                }
            }
        } catch (e: Exception) {
            Log.e(TAG, "Error processing captured image", e)
        } finally {
            image?.close()
        }
    }

    private fun imageToBitmap(image: Image): Bitmap? {
        return try {
            val planes = image.planes
            val buffer = planes[0].buffer
            val pixelStride = planes[0].pixelStride
            val rowStride = planes[0].rowStride
            val rowPadding = rowStride - pixelStride * screenWidth

            val bitmap = Bitmap.createBitmap(
                screenWidth + rowPadding / pixelStride,
                screenHeight,
                Bitmap.Config.ARGB_8888
            )
            bitmap.copyPixelsFromBuffer(buffer)
            
            // Crop to remove padding if necessary
            if (rowPadding == 0) {
                bitmap
            } else {
                Bitmap.createBitmap(bitmap, 0, 0, screenWidth, screenHeight)
            }
        } catch (e: Exception) {
            Log.e(TAG, "Failed to convert image to bitmap", e)
            null
        }
    }

    private suspend fun processCapturedBitmap(bitmap: Bitmap) {
        withContext(Dispatchers.Default) {
            try {
                Log.d(TAG, "Processing captured frame: ${bitmap.width}x${bitmap.height}")

                // Analyze the captured image
                val analysisResult = imageAnalyzer.analyzeScreenCapture(bitmap)

                Log.d(TAG, "Analysis result: ${analysisResult.gameState}, " +
                        "confidence: ${analysisResult.confidence}, " +
                        "detected brawlers: ${analysisResult.detectedBrawlers.size}")

                // Send analysis result to overlay service via broadcast
                val intent = android.content.Intent("com.example.brawldraft.ANALYSIS_RESULT").apply {
                    putExtra("game_state", analysisResult.gameState.name)
                    putExtra("confidence", analysisResult.confidence)
                    putExtra("detected_brawlers_count", analysisResult.detectedBrawlers.size)
                    putExtra("current_phase", analysisResult.currentPhase.name)
                    analysisResult.detectedMap?.let { map ->
                        putExtra("detected_map", map.name)
                    }
                }
                sendBroadcast(intent)

            } catch (e: Exception) {
                Log.e(TAG, "Error processing bitmap", e)
            } finally {
                bitmap.recycle()
            }
        }
    }

    private fun stopCapture() {
        captureJob?.cancel()
        
        virtualDisplay?.release()
        virtualDisplay = null
        
        imageReader?.close()
        imageReader = null
        
        mediaProjection?.stop()
        mediaProjection = null
        
        Log.d(TAG, "Screen capture stopped")
    }

    private fun createNotification(): Notification {
        return NotificationCompat.Builder(this, CHANNEL_ID)
            .setContentTitle("Brawl Draft Helper")
            .setContentText("Analyzing screen for draft detection")
            .setSmallIcon(android.R.drawable.ic_menu_camera)
            .setOngoing(true)
            .setPriority(NotificationCompat.PRIORITY_LOW)
            .build()
    }

    override fun onDestroy() {
        super.onDestroy()
        stopCapture()
        templateMatcher.cleanup()
        serviceScope.cancel()
    }
}
