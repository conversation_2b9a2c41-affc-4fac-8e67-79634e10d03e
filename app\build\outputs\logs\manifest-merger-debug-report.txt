-- Merging decision tree log ---
manifest
ADDED from C:\Users\<USER>\AndroidStudioProjects\Brawldraft\app\src\main\AndroidManifest.xml:2:1-41:12
INJECTED from C:\Users\<USER>\AndroidStudioProjects\Brawldraft\app\src\main\AndroidManifest.xml:2:1-41:12
INJECTED from C:\Users\<USER>\AndroidStudioProjects\Brawldraft\app\src\main\AndroidManifest.xml:2:1-41:12
INJECTED from C:\Users\<USER>\AndroidStudioProjects\Brawldraft\app\src\main\AndroidManifest.xml:2:1-41:12
MERGED from [androidx.databinding:viewbinding:8.12.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\5643c4abd16e2491f20a663d7ef06c9a\transformed\viewbinding-8.12.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.google.android.material:material:1.10.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\2aa33be5c222f8571691eafceec0feae\transformed\material-1.10.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.appcompat:appcompat-resources:1.6.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\4096dd652f32b5650f45f01341840dee\transformed\appcompat-resources-1.6.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.constraintlayout:constraintlayout:2.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\6c8e9496ebc4f4825b7d92481101508e\transformed\constraintlayout-2.0.1\AndroidManifest.xml:2:1-11:12
MERGED from [androidx.appcompat:appcompat:1.6.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\57562ad43921b16639a6438f83b538f4\transformed\appcompat-1.6.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.viewpager2:viewpager2:1.1.0-beta02] C:\Users\<USER>\.gradle\caches\8.13\transforms\6a085a8532b5f1c9cc92a705072878ec\transformed\viewpager2-1.1.0-beta02\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.recyclerview:recyclerview:1.3.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\bd79d5b2732e6409104ae0a235ed2257\transformed\recyclerview-1.3.2\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.emoji2:emoji2-views-helper:1.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\85ccf59a35ef621fe31b70fd11c949ca\transformed\emoji2-views-helper-1.2.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\329078bde69d4dba3eee3c51fa507581\transformed\emoji2-1.2.0\AndroidManifest.xml:17:1-35:12
MERGED from [androidx.fragment:fragment:1.3.6] C:\Users\<USER>\.gradle\caches\8.13\transforms\3811f5a2405f907a6317403b0ed6d5c5\transformed\fragment-1.3.6\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.activity:activity:1.8.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\c29c65109bdd7c056436a6f765c7cddf\transformed\activity-1.8.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.drawerlayout:drawerlayout:1.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\ffcb593e47fcc22326b53ac279701469\transformed\drawerlayout-1.1.1\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.coordinatorlayout:coordinatorlayout:1.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\bd8ada4441c05c95954e6db107c0a527\transformed\coordinatorlayout-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.dynamicanimation:dynamicanimation:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\f1173d438ce4847e18cedd7ede9909d3\transformed\dynamicanimation-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.transition:transition:1.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\d7bb9d7d5b1337325653502b9e12f922\transformed\transition-1.2.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ce4290fb4a021d2daf1f7e588a3e26f2\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\4a58b1885dd9c8765791eabb8e5bb545\transformed\vectordrawable-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ac39490e487199f7805c1fd2d236191e\transformed\viewpager-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\df7fdd527d26880f879ce0b91c28732c\transformed\legacy-support-core-utils-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.loader:loader:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\f6bd29e813ecdd396e486522d2ef84e3\transformed\loader-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.customview:customview:1.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\e33617d915bcb73cdf34d4d6ccc10d96\transformed\customview-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.core:core:1.10.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\27d9032238570e73400cc8465b116304\transformed\core-1.10.1\AndroidManifest.xml:17:1-30:12
MERGED from [androidx.customview:customview-poolingcontainer:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\2b7f04ee6f8fa951ce50cebf6a4f0195\transformed\customview-poolingcontainer-1.0.0\AndroidManifest.xml:17:1-23:12
MERGED from [androidx.savedstate:savedstate:1.2.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\cd89a1c252a892751ff7c95f8aad07a3\transformed\savedstate-1.2.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-process:2.6.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\b158b40abb2bffb0911c41424820a72c\transformed\lifecycle-process-2.6.1\AndroidManifest.xml:17:1-35:12
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.6.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\f86b5199403a3b13746d2265f42c734f\transformed\lifecycle-livedata-core-2.6.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-livedata:2.6.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\418ed0567bce10e31e6c6e6436445be8\transformed\lifecycle-livedata-2.6.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.6.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\c0f15dbdc98585d31c1d7ff8be8a688d\transformed\lifecycle-viewmodel-2.6.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\775498b641299fa125cda62bad9fa637\transformed\lifecycle-runtime-2.6.1\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.6.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\cf9ef6cda408e9252619121bc4812f09\transformed\lifecycle-viewmodel-savedstate-2.6.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.core:core-ktx:1.10.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\b3e583b5ded9b4491ec075b42ca57639\transformed\core-ktx-1.10.1\AndroidManifest.xml:2:1-7:12
MERGED from [:opencv] C:\Users\<USER>\AndroidStudioProjects\Brawldraft\opencv\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.annotation:annotation-experimental:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\fd76b7d935ba7670a4a71a4a3027c4c4\transformed\annotation-experimental-1.3.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.cardview:cardview:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\f6e9302b05bad435dca762a66d4a56c9\transformed\cardview-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\8820832247cf8326505de0433681ae42\transformed\cursoradapter-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\01f90f13dce69010d3d45ad67f1e9c11\transformed\interpolator-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\43ddc9d524280c2767250f7bc63bb4fa\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:17:1-27:12
MERGED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\efb3be5c26ff3e55d07c351acc9bddda\transformed\profileinstaller-1.3.0\AndroidManifest.xml:17:1-55:12
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\88c6766bea3319a0b1f927b94b88c639\transformed\startup-runtime-1.1.1\AndroidManifest.xml:17:1-33:12
MERGED from [androidx.tracing:tracing:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\bb069d9c6d6f188a2fa09ec338ba96e8\transformed\tracing-1.0.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\19ee34a81ef8e7dd2113cfd8c7b6e61a\transformed\core-runtime-2.2.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.documentfile:documentfile:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\f2e7e87d8a921e371147be95da2965bb\transformed\documentfile-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\e67a59d76274442eb29385c3a02f674f\transformed\localbroadcastmanager-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.print:print:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\361e2def74492df06bc1674cf5458325\transformed\print-1.0.0\AndroidManifest.xml:17:1-22:12
	package
		INJECTED from C:\Users\<USER>\AndroidStudioProjects\Brawldraft\app\src\main\AndroidManifest.xml
	android:versionName
		INJECTED from C:\Users\<USER>\AndroidStudioProjects\Brawldraft\app\src\main\AndroidManifest.xml
	xmlns:tools
		ADDED from C:\Users\<USER>\AndroidStudioProjects\Brawldraft\app\src\main\AndroidManifest.xml:3:5-51
	android:versionCode
		INJECTED from C:\Users\<USER>\AndroidStudioProjects\Brawldraft\app\src\main\AndroidManifest.xml
	xmlns:android
		ADDED from C:\Users\<USER>\AndroidStudioProjects\Brawldraft\app\src\main\AndroidManifest.xml:2:11-69
uses-permission#android.permission.SYSTEM_ALERT_WINDOW
ADDED from C:\Users\<USER>\AndroidStudioProjects\Brawldraft\app\src\main\AndroidManifest.xml:5:5-78
	android:name
		ADDED from C:\Users\<USER>\AndroidStudioProjects\Brawldraft\app\src\main\AndroidManifest.xml:5:22-75
uses-permission#android.permission.FOREGROUND_SERVICE
ADDED from C:\Users\<USER>\AndroidStudioProjects\Brawldraft\app\src\main\AndroidManifest.xml:6:5-77
	android:name
		ADDED from C:\Users\<USER>\AndroidStudioProjects\Brawldraft\app\src\main\AndroidManifest.xml:6:22-74
uses-permission#android.permission.FOREGROUND_SERVICE_MEDIA_PROJECTION
ADDED from C:\Users\<USER>\AndroidStudioProjects\Brawldraft\app\src\main\AndroidManifest.xml:7:5-94
	android:name
		ADDED from C:\Users\<USER>\AndroidStudioProjects\Brawldraft\app\src\main\AndroidManifest.xml:7:22-91
application
ADDED from C:\Users\<USER>\AndroidStudioProjects\Brawldraft\app\src\main\AndroidManifest.xml:9:5-39:19
INJECTED from C:\Users\<USER>\AndroidStudioProjects\Brawldraft\app\src\main\AndroidManifest.xml:9:5-39:19
MERGED from [com.google.android.material:material:1.10.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\2aa33be5c222f8571691eafceec0feae\transformed\material-1.10.0\AndroidManifest.xml:22:5-20
MERGED from [com.google.android.material:material:1.10.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\2aa33be5c222f8571691eafceec0feae\transformed\material-1.10.0\AndroidManifest.xml:22:5-20
MERGED from [androidx.constraintlayout:constraintlayout:2.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\6c8e9496ebc4f4825b7d92481101508e\transformed\constraintlayout-2.0.1\AndroidManifest.xml:9:5-20
MERGED from [androidx.constraintlayout:constraintlayout:2.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\6c8e9496ebc4f4825b7d92481101508e\transformed\constraintlayout-2.0.1\AndroidManifest.xml:9:5-20
MERGED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\329078bde69d4dba3eee3c51fa507581\transformed\emoji2-1.2.0\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\329078bde69d4dba3eee3c51fa507581\transformed\emoji2-1.2.0\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.core:core:1.10.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\27d9032238570e73400cc8465b116304\transformed\core-1.10.1\AndroidManifest.xml:28:5-89
MERGED from [androidx.core:core:1.10.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\27d9032238570e73400cc8465b116304\transformed\core-1.10.1\AndroidManifest.xml:28:5-89
MERGED from [androidx.lifecycle:lifecycle-process:2.6.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\b158b40abb2bffb0911c41424820a72c\transformed\lifecycle-process-2.6.1\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.lifecycle:lifecycle-process:2.6.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\b158b40abb2bffb0911c41424820a72c\transformed\lifecycle-process-2.6.1\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\43ddc9d524280c2767250f7bc63bb4fa\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:24:5-25:19
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\43ddc9d524280c2767250f7bc63bb4fa\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:24:5-25:19
MERGED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\efb3be5c26ff3e55d07c351acc9bddda\transformed\profileinstaller-1.3.0\AndroidManifest.xml:23:5-53:19
MERGED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\efb3be5c26ff3e55d07c351acc9bddda\transformed\profileinstaller-1.3.0\AndroidManifest.xml:23:5-53:19
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\88c6766bea3319a0b1f927b94b88c639\transformed\startup-runtime-1.1.1\AndroidManifest.xml:25:5-31:19
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\88c6766bea3319a0b1f927b94b88c639\transformed\startup-runtime-1.1.1\AndroidManifest.xml:25:5-31:19
	android:extractNativeLibs
		INJECTED from C:\Users\<USER>\AndroidStudioProjects\Brawldraft\app\src\main\AndroidManifest.xml
	android:appComponentFactory
		ADDED from [androidx.core:core:1.10.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\27d9032238570e73400cc8465b116304\transformed\core-1.10.1\AndroidManifest.xml:28:18-86
	android:supportsRtl
		ADDED from C:\Users\<USER>\AndroidStudioProjects\Brawldraft\app\src\main\AndroidManifest.xml:16:9-35
	android:label
		ADDED from C:\Users\<USER>\AndroidStudioProjects\Brawldraft\app\src\main\AndroidManifest.xml:14:9-41
	android:fullBackupContent
		ADDED from C:\Users\<USER>\AndroidStudioProjects\Brawldraft\app\src\main\AndroidManifest.xml:12:9-54
	android:roundIcon
		ADDED from C:\Users\<USER>\AndroidStudioProjects\Brawldraft\app\src\main\AndroidManifest.xml:15:9-54
	android:icon
		ADDED from C:\Users\<USER>\AndroidStudioProjects\Brawldraft\app\src\main\AndroidManifest.xml:13:9-43
	android:allowBackup
		ADDED from C:\Users\<USER>\AndroidStudioProjects\Brawldraft\app\src\main\AndroidManifest.xml:10:9-35
	android:theme
		ADDED from C:\Users\<USER>\AndroidStudioProjects\Brawldraft\app\src\main\AndroidManifest.xml:17:9-48
	android:dataExtractionRules
		ADDED from C:\Users\<USER>\AndroidStudioProjects\Brawldraft\app\src\main\AndroidManifest.xml:11:9-65
activity#com.example.brawldraft.MainActivity
ADDED from C:\Users\<USER>\AndroidStudioProjects\Brawldraft\app\src\main\AndroidManifest.xml:18:9-26:20
	android:exported
		ADDED from C:\Users\<USER>\AndroidStudioProjects\Brawldraft\app\src\main\AndroidManifest.xml:20:13-36
	android:name
		ADDED from C:\Users\<USER>\AndroidStudioProjects\Brawldraft\app\src\main\AndroidManifest.xml:19:13-41
intent-filter#action:name:android.intent.action.MAIN+category:name:android.intent.category.LAUNCHER
ADDED from C:\Users\<USER>\AndroidStudioProjects\Brawldraft\app\src\main\AndroidManifest.xml:21:13-25:29
action#android.intent.action.MAIN
ADDED from C:\Users\<USER>\AndroidStudioProjects\Brawldraft\app\src\main\AndroidManifest.xml:22:17-69
	android:name
		ADDED from C:\Users\<USER>\AndroidStudioProjects\Brawldraft\app\src\main\AndroidManifest.xml:22:25-66
category#android.intent.category.LAUNCHER
ADDED from C:\Users\<USER>\AndroidStudioProjects\Brawldraft\app\src\main\AndroidManifest.xml:24:17-77
	android:name
		ADDED from C:\Users\<USER>\AndroidStudioProjects\Brawldraft\app\src\main\AndroidManifest.xml:24:27-74
activity#com.example.brawldraft.ScreenCapturePermissionActivity
ADDED from C:\Users\<USER>\AndroidStudioProjects\Brawldraft\app\src\main\AndroidManifest.xml:28:9-31:40
	android:exported
		ADDED from C:\Users\<USER>\AndroidStudioProjects\Brawldraft\app\src\main\AndroidManifest.xml:31:13-37
	android:theme
		ADDED from C:\Users\<USER>\AndroidStudioProjects\Brawldraft\app\src\main\AndroidManifest.xml:30:13-72
	android:name
		ADDED from C:\Users\<USER>\AndroidStudioProjects\Brawldraft\app\src\main\AndroidManifest.xml:29:13-60
service#com.example.brawldraft.FloatingOverlayService
ADDED from C:\Users\<USER>\AndroidStudioProjects\Brawldraft\app\src\main\AndroidManifest.xml:33:9-59
	android:name
		ADDED from C:\Users\<USER>\AndroidStudioProjects\Brawldraft\app\src\main\AndroidManifest.xml:33:18-56
service#com.example.brawldraft.ScreenCaptureService
ADDED from C:\Users\<USER>\AndroidStudioProjects\Brawldraft\app\src\main\AndroidManifest.xml:35:9-37:63
	android:foregroundServiceType
		ADDED from C:\Users\<USER>\AndroidStudioProjects\Brawldraft\app\src\main\AndroidManifest.xml:37:13-60
	android:name
		ADDED from C:\Users\<USER>\AndroidStudioProjects\Brawldraft\app\src\main\AndroidManifest.xml:36:13-49
uses-sdk
INJECTED from C:\Users\<USER>\AndroidStudioProjects\Brawldraft\app\src\main\AndroidManifest.xml reason: use-sdk injection requested
INJECTED from C:\Users\<USER>\AndroidStudioProjects\Brawldraft\app\src\main\AndroidManifest.xml
INJECTED from C:\Users\<USER>\AndroidStudioProjects\Brawldraft\app\src\main\AndroidManifest.xml
MERGED from [androidx.databinding:viewbinding:8.12.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\5643c4abd16e2491f20a663d7ef06c9a\transformed\viewbinding-8.12.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.databinding:viewbinding:8.12.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\5643c4abd16e2491f20a663d7ef06c9a\transformed\viewbinding-8.12.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.material:material:1.10.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\2aa33be5c222f8571691eafceec0feae\transformed\material-1.10.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.material:material:1.10.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\2aa33be5c222f8571691eafceec0feae\transformed\material-1.10.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.appcompat:appcompat-resources:1.6.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\4096dd652f32b5650f45f01341840dee\transformed\appcompat-resources-1.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.appcompat:appcompat-resources:1.6.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\4096dd652f32b5650f45f01341840dee\transformed\appcompat-resources-1.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.constraintlayout:constraintlayout:2.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\6c8e9496ebc4f4825b7d92481101508e\transformed\constraintlayout-2.0.1\AndroidManifest.xml:5:5-7:41
MERGED from [androidx.constraintlayout:constraintlayout:2.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\6c8e9496ebc4f4825b7d92481101508e\transformed\constraintlayout-2.0.1\AndroidManifest.xml:5:5-7:41
MERGED from [androidx.appcompat:appcompat:1.6.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\57562ad43921b16639a6438f83b538f4\transformed\appcompat-1.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.appcompat:appcompat:1.6.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\57562ad43921b16639a6438f83b538f4\transformed\appcompat-1.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.viewpager2:viewpager2:1.1.0-beta02] C:\Users\<USER>\.gradle\caches\8.13\transforms\6a085a8532b5f1c9cc92a705072878ec\transformed\viewpager2-1.1.0-beta02\AndroidManifest.xml:5:5-44
MERGED from [androidx.viewpager2:viewpager2:1.1.0-beta02] C:\Users\<USER>\.gradle\caches\8.13\transforms\6a085a8532b5f1c9cc92a705072878ec\transformed\viewpager2-1.1.0-beta02\AndroidManifest.xml:5:5-44
MERGED from [androidx.recyclerview:recyclerview:1.3.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\bd79d5b2732e6409104ae0a235ed2257\transformed\recyclerview-1.3.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.recyclerview:recyclerview:1.3.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\bd79d5b2732e6409104ae0a235ed2257\transformed\recyclerview-1.3.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.emoji2:emoji2-views-helper:1.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\85ccf59a35ef621fe31b70fd11c949ca\transformed\emoji2-views-helper-1.2.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.emoji2:emoji2-views-helper:1.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\85ccf59a35ef621fe31b70fd11c949ca\transformed\emoji2-views-helper-1.2.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\329078bde69d4dba3eee3c51fa507581\transformed\emoji2-1.2.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\329078bde69d4dba3eee3c51fa507581\transformed\emoji2-1.2.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.fragment:fragment:1.3.6] C:\Users\<USER>\.gradle\caches\8.13\transforms\3811f5a2405f907a6317403b0ed6d5c5\transformed\fragment-1.3.6\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.fragment:fragment:1.3.6] C:\Users\<USER>\.gradle\caches\8.13\transforms\3811f5a2405f907a6317403b0ed6d5c5\transformed\fragment-1.3.6\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.activity:activity:1.8.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\c29c65109bdd7c056436a6f765c7cddf\transformed\activity-1.8.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.activity:activity:1.8.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\c29c65109bdd7c056436a6f765c7cddf\transformed\activity-1.8.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.drawerlayout:drawerlayout:1.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\ffcb593e47fcc22326b53ac279701469\transformed\drawerlayout-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.drawerlayout:drawerlayout:1.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\ffcb593e47fcc22326b53ac279701469\transformed\drawerlayout-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.coordinatorlayout:coordinatorlayout:1.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\bd8ada4441c05c95954e6db107c0a527\transformed\coordinatorlayout-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.coordinatorlayout:coordinatorlayout:1.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\bd8ada4441c05c95954e6db107c0a527\transformed\coordinatorlayout-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.dynamicanimation:dynamicanimation:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\f1173d438ce4847e18cedd7ede9909d3\transformed\dynamicanimation-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.dynamicanimation:dynamicanimation:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\f1173d438ce4847e18cedd7ede9909d3\transformed\dynamicanimation-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.transition:transition:1.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\d7bb9d7d5b1337325653502b9e12f922\transformed\transition-1.2.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.transition:transition:1.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\d7bb9d7d5b1337325653502b9e12f922\transformed\transition-1.2.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ce4290fb4a021d2daf1f7e588a3e26f2\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ce4290fb4a021d2daf1f7e588a3e26f2\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\4a58b1885dd9c8765791eabb8e5bb545\transformed\vectordrawable-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\4a58b1885dd9c8765791eabb8e5bb545\transformed\vectordrawable-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ac39490e487199f7805c1fd2d236191e\transformed\viewpager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ac39490e487199f7805c1fd2d236191e\transformed\viewpager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\df7fdd527d26880f879ce0b91c28732c\transformed\legacy-support-core-utils-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\df7fdd527d26880f879ce0b91c28732c\transformed\legacy-support-core-utils-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.loader:loader:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\f6bd29e813ecdd396e486522d2ef84e3\transformed\loader-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.loader:loader:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\f6bd29e813ecdd396e486522d2ef84e3\transformed\loader-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.customview:customview:1.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\e33617d915bcb73cdf34d4d6ccc10d96\transformed\customview-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.customview:customview:1.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\e33617d915bcb73cdf34d4d6ccc10d96\transformed\customview-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.core:core:1.10.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\27d9032238570e73400cc8465b116304\transformed\core-1.10.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.core:core:1.10.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\27d9032238570e73400cc8465b116304\transformed\core-1.10.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.customview:customview-poolingcontainer:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\2b7f04ee6f8fa951ce50cebf6a4f0195\transformed\customview-poolingcontainer-1.0.0\AndroidManifest.xml:20:5-21:38
MERGED from [androidx.customview:customview-poolingcontainer:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\2b7f04ee6f8fa951ce50cebf6a4f0195\transformed\customview-poolingcontainer-1.0.0\AndroidManifest.xml:20:5-21:38
MERGED from [androidx.savedstate:savedstate:1.2.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\cd89a1c252a892751ff7c95f8aad07a3\transformed\savedstate-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate:1.2.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\cd89a1c252a892751ff7c95f8aad07a3\transformed\savedstate-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-process:2.6.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\b158b40abb2bffb0911c41424820a72c\transformed\lifecycle-process-2.6.1\AndroidManifest.xml:21:5-44
MERGED from [androidx.lifecycle:lifecycle-process:2.6.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\b158b40abb2bffb0911c41424820a72c\transformed\lifecycle-process-2.6.1\AndroidManifest.xml:21:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.6.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\f86b5199403a3b13746d2265f42c734f\transformed\lifecycle-livedata-core-2.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.6.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\f86b5199403a3b13746d2265f42c734f\transformed\lifecycle-livedata-core-2.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata:2.6.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\418ed0567bce10e31e6c6e6436445be8\transformed\lifecycle-livedata-2.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata:2.6.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\418ed0567bce10e31e6c6e6436445be8\transformed\lifecycle-livedata-2.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.6.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\c0f15dbdc98585d31c1d7ff8be8a688d\transformed\lifecycle-viewmodel-2.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.6.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\c0f15dbdc98585d31c1d7ff8be8a688d\transformed\lifecycle-viewmodel-2.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\775498b641299fa125cda62bad9fa637\transformed\lifecycle-runtime-2.6.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\775498b641299fa125cda62bad9fa637\transformed\lifecycle-runtime-2.6.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.6.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\cf9ef6cda408e9252619121bc4812f09\transformed\lifecycle-viewmodel-savedstate-2.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.6.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\cf9ef6cda408e9252619121bc4812f09\transformed\lifecycle-viewmodel-savedstate-2.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.core:core-ktx:1.10.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\b3e583b5ded9b4491ec075b42ca57639\transformed\core-ktx-1.10.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.core:core-ktx:1.10.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\b3e583b5ded9b4491ec075b42ca57639\transformed\core-ktx-1.10.1\AndroidManifest.xml:5:5-44
MERGED from [:opencv] C:\Users\<USER>\AndroidStudioProjects\Brawldraft\opencv\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:opencv] C:\Users\<USER>\AndroidStudioProjects\Brawldraft\opencv\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [androidx.annotation:annotation-experimental:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\fd76b7d935ba7670a4a71a4a3027c4c4\transformed\annotation-experimental-1.3.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.annotation:annotation-experimental:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\fd76b7d935ba7670a4a71a4a3027c4c4\transformed\annotation-experimental-1.3.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.cardview:cardview:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\f6e9302b05bad435dca762a66d4a56c9\transformed\cardview-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.cardview:cardview:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\f6e9302b05bad435dca762a66d4a56c9\transformed\cardview-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\8820832247cf8326505de0433681ae42\transformed\cursoradapter-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\8820832247cf8326505de0433681ae42\transformed\cursoradapter-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\01f90f13dce69010d3d45ad67f1e9c11\transformed\interpolator-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\01f90f13dce69010d3d45ad67f1e9c11\transformed\interpolator-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\43ddc9d524280c2767250f7bc63bb4fa\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\43ddc9d524280c2767250f7bc63bb4fa\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\efb3be5c26ff3e55d07c351acc9bddda\transformed\profileinstaller-1.3.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\efb3be5c26ff3e55d07c351acc9bddda\transformed\profileinstaller-1.3.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\88c6766bea3319a0b1f927b94b88c639\transformed\startup-runtime-1.1.1\AndroidManifest.xml:21:5-23:41
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\88c6766bea3319a0b1f927b94b88c639\transformed\startup-runtime-1.1.1\AndroidManifest.xml:21:5-23:41
MERGED from [androidx.tracing:tracing:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\bb069d9c6d6f188a2fa09ec338ba96e8\transformed\tracing-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.tracing:tracing:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\bb069d9c6d6f188a2fa09ec338ba96e8\transformed\tracing-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\19ee34a81ef8e7dd2113cfd8c7b6e61a\transformed\core-runtime-2.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\19ee34a81ef8e7dd2113cfd8c7b6e61a\transformed\core-runtime-2.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.documentfile:documentfile:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\f2e7e87d8a921e371147be95da2965bb\transformed\documentfile-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.documentfile:documentfile:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\f2e7e87d8a921e371147be95da2965bb\transformed\documentfile-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\e67a59d76274442eb29385c3a02f674f\transformed\localbroadcastmanager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\e67a59d76274442eb29385c3a02f674f\transformed\localbroadcastmanager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.print:print:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\361e2def74492df06bc1674cf5458325\transformed\print-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.print:print:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\361e2def74492df06bc1674cf5458325\transformed\print-1.0.0\AndroidManifest.xml:20:5-44
	android:targetSdkVersion
		INJECTED from C:\Users\<USER>\AndroidStudioProjects\Brawldraft\app\src\main\AndroidManifest.xml
	android:minSdkVersion
		INJECTED from C:\Users\<USER>\AndroidStudioProjects\Brawldraft\app\src\main\AndroidManifest.xml
provider#androidx.startup.InitializationProvider
ADDED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\329078bde69d4dba3eee3c51fa507581\transformed\emoji2-1.2.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.lifecycle:lifecycle-process:2.6.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\b158b40abb2bffb0911c41424820a72c\transformed\lifecycle-process-2.6.1\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.lifecycle:lifecycle-process:2.6.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\b158b40abb2bffb0911c41424820a72c\transformed\lifecycle-process-2.6.1\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\efb3be5c26ff3e55d07c351acc9bddda\transformed\profileinstaller-1.3.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\efb3be5c26ff3e55d07c351acc9bddda\transformed\profileinstaller-1.3.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\88c6766bea3319a0b1f927b94b88c639\transformed\startup-runtime-1.1.1\AndroidManifest.xml:26:9-30:34
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\88c6766bea3319a0b1f927b94b88c639\transformed\startup-runtime-1.1.1\AndroidManifest.xml:26:9-30:34
	tools:node
		ADDED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\329078bde69d4dba3eee3c51fa507581\transformed\emoji2-1.2.0\AndroidManifest.xml:28:13-31
	android:authorities
		ADDED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\329078bde69d4dba3eee3c51fa507581\transformed\emoji2-1.2.0\AndroidManifest.xml:26:13-68
	android:exported
		ADDED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\329078bde69d4dba3eee3c51fa507581\transformed\emoji2-1.2.0\AndroidManifest.xml:27:13-37
	android:name
		ADDED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\329078bde69d4dba3eee3c51fa507581\transformed\emoji2-1.2.0\AndroidManifest.xml:25:13-67
meta-data#androidx.emoji2.text.EmojiCompatInitializer
ADDED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\329078bde69d4dba3eee3c51fa507581\transformed\emoji2-1.2.0\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\329078bde69d4dba3eee3c51fa507581\transformed\emoji2-1.2.0\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\329078bde69d4dba3eee3c51fa507581\transformed\emoji2-1.2.0\AndroidManifest.xml:30:17-75
permission#${applicationId}.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.10.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\27d9032238570e73400cc8465b116304\transformed\core-1.10.1\AndroidManifest.xml:22:5-24:47
	android:protectionLevel
		ADDED from [androidx.core:core:1.10.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\27d9032238570e73400cc8465b116304\transformed\core-1.10.1\AndroidManifest.xml:24:9-44
	android:name
		ADDED from [androidx.core:core:1.10.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\27d9032238570e73400cc8465b116304\transformed\core-1.10.1\AndroidManifest.xml:23:9-81
permission#com.example.brawldraft.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.10.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\27d9032238570e73400cc8465b116304\transformed\core-1.10.1\AndroidManifest.xml:22:5-24:47
	android:protectionLevel
		ADDED from [androidx.core:core:1.10.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\27d9032238570e73400cc8465b116304\transformed\core-1.10.1\AndroidManifest.xml:24:9-44
	android:name
		ADDED from [androidx.core:core:1.10.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\27d9032238570e73400cc8465b116304\transformed\core-1.10.1\AndroidManifest.xml:23:9-81
uses-permission#${applicationId}.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.10.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\27d9032238570e73400cc8465b116304\transformed\core-1.10.1\AndroidManifest.xml:26:5-97
	android:name
		ADDED from [androidx.core:core:1.10.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\27d9032238570e73400cc8465b116304\transformed\core-1.10.1\AndroidManifest.xml:26:22-94
uses-permission#com.example.brawldraft.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.10.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\27d9032238570e73400cc8465b116304\transformed\core-1.10.1\AndroidManifest.xml:26:5-97
	android:name
		ADDED from [androidx.core:core:1.10.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\27d9032238570e73400cc8465b116304\transformed\core-1.10.1\AndroidManifest.xml:26:22-94
meta-data#androidx.lifecycle.ProcessLifecycleInitializer
ADDED from [androidx.lifecycle:lifecycle-process:2.6.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\b158b40abb2bffb0911c41424820a72c\transformed\lifecycle-process-2.6.1\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.lifecycle:lifecycle-process:2.6.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\b158b40abb2bffb0911c41424820a72c\transformed\lifecycle-process-2.6.1\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.lifecycle:lifecycle-process:2.6.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\b158b40abb2bffb0911c41424820a72c\transformed\lifecycle-process-2.6.1\AndroidManifest.xml:30:17-78
meta-data#androidx.profileinstaller.ProfileInstallerInitializer
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\efb3be5c26ff3e55d07c351acc9bddda\transformed\profileinstaller-1.3.0\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\efb3be5c26ff3e55d07c351acc9bddda\transformed\profileinstaller-1.3.0\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\efb3be5c26ff3e55d07c351acc9bddda\transformed\profileinstaller-1.3.0\AndroidManifest.xml:30:17-85
receiver#androidx.profileinstaller.ProfileInstallReceiver
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\efb3be5c26ff3e55d07c351acc9bddda\transformed\profileinstaller-1.3.0\AndroidManifest.xml:34:9-52:20
	android:enabled
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\efb3be5c26ff3e55d07c351acc9bddda\transformed\profileinstaller-1.3.0\AndroidManifest.xml:37:13-35
	android:exported
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\efb3be5c26ff3e55d07c351acc9bddda\transformed\profileinstaller-1.3.0\AndroidManifest.xml:38:13-36
	android:permission
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\efb3be5c26ff3e55d07c351acc9bddda\transformed\profileinstaller-1.3.0\AndroidManifest.xml:39:13-57
	android:directBootAware
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\efb3be5c26ff3e55d07c351acc9bddda\transformed\profileinstaller-1.3.0\AndroidManifest.xml:36:13-44
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\efb3be5c26ff3e55d07c351acc9bddda\transformed\profileinstaller-1.3.0\AndroidManifest.xml:35:13-76
intent-filter#action:name:androidx.profileinstaller.action.INSTALL_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\efb3be5c26ff3e55d07c351acc9bddda\transformed\profileinstaller-1.3.0\AndroidManifest.xml:40:13-42:29
action#androidx.profileinstaller.action.INSTALL_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\efb3be5c26ff3e55d07c351acc9bddda\transformed\profileinstaller-1.3.0\AndroidManifest.xml:41:17-91
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\efb3be5c26ff3e55d07c351acc9bddda\transformed\profileinstaller-1.3.0\AndroidManifest.xml:41:25-88
intent-filter#action:name:androidx.profileinstaller.action.SKIP_FILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\efb3be5c26ff3e55d07c351acc9bddda\transformed\profileinstaller-1.3.0\AndroidManifest.xml:43:13-45:29
action#androidx.profileinstaller.action.SKIP_FILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\efb3be5c26ff3e55d07c351acc9bddda\transformed\profileinstaller-1.3.0\AndroidManifest.xml:44:17-85
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\efb3be5c26ff3e55d07c351acc9bddda\transformed\profileinstaller-1.3.0\AndroidManifest.xml:44:25-82
intent-filter#action:name:androidx.profileinstaller.action.SAVE_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\efb3be5c26ff3e55d07c351acc9bddda\transformed\profileinstaller-1.3.0\AndroidManifest.xml:46:13-48:29
action#androidx.profileinstaller.action.SAVE_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\efb3be5c26ff3e55d07c351acc9bddda\transformed\profileinstaller-1.3.0\AndroidManifest.xml:47:17-88
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\efb3be5c26ff3e55d07c351acc9bddda\transformed\profileinstaller-1.3.0\AndroidManifest.xml:47:25-85
intent-filter#action:name:androidx.profileinstaller.action.BENCHMARK_OPERATION
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\efb3be5c26ff3e55d07c351acc9bddda\transformed\profileinstaller-1.3.0\AndroidManifest.xml:49:13-51:29
action#androidx.profileinstaller.action.BENCHMARK_OPERATION
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\efb3be5c26ff3e55d07c351acc9bddda\transformed\profileinstaller-1.3.0\AndroidManifest.xml:50:17-95
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\efb3be5c26ff3e55d07c351acc9bddda\transformed\profileinstaller-1.3.0\AndroidManifest.xml:50:25-92
