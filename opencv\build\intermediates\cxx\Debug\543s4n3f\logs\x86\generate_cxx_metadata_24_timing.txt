# C/C++ build system timings
generate_cxx_metadata
  [gap of 98ms]
  create-invalidation-state 269ms
  [gap of 315ms]
  write-metadata-json-to-file 29ms
generate_cxx_metadata completed in 715ms

# C/C++ build system timings
generate_cxx_metadata
  [gap of 24ms]
  create-invalidation-state 65ms
  [gap of 42ms]
  write-metadata-json-to-file 14ms
generate_cxx_metadata completed in 145ms

# C/C++ build system timings
generate_cxx_metadata
  [gap of 22ms]
  create-invalidation-state 73ms
  [gap of 46ms]
  write-metadata-json-to-file 12ms
generate_cxx_metadata completed in 155ms

